/**
 * Detailed API Endpoint Policies
 * Specific policies for each API endpoint in the system
 */

import {
  MODULES,
  ACTIONS,
  POLICY_TYPES,
  POLICY_EFFECTS,
} from './constants.js';
import type { PolicyConfiguration } from './types.js';

/**
 * Camera API Policies
 */
export const CAMERA_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'camera-list',
    description: 'GET /api/cameras - List all cameras',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.READ },
    resources: ['cameras'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'camera-view-by-type',
    description: 'GET /api/cameras/type/:type - View cameras by type',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.READ },
    resources: ['cameras/type'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'camera-view-by-status',
    description: 'GET /api/cameras/status/:status - View cameras by status',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.READ },
    resources: ['cameras/status'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'camera-view-by-ip',
    description: 'GET /api/cameras/ip/:ipAddress - View camera by IP',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.READ },
    resources: ['cameras/ip'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'camera-view-by-location',
    description: 'GET /api/cameras/location/:location - View cameras by location',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.READ },
    resources: ['cameras/location'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'camera-view-details',
    description: 'GET /api/cameras/:id - View camera details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.READ },
    resources: ['cameras/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * User API Policies
 */
export const USER_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'user-list',
    description: 'GET /api/users - List all users',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.READ },
    resources: ['users'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'user-view-by-unit',
    description: 'GET /api/users/unit/:unitId - View users by unit',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.READ },
    resources: ['users/unit'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'user-view-by-role',
    description: 'GET /api/users/role/:memberRoleId - View users by role',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.READ },
    resources: ['users/role'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'user-view-details',
    description: 'GET /api/users/:id - View user details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.READ },
    resources: ['users/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'user-create',
    description: 'POST /api/users - Create new user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.CREATE },
    resources: ['users'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'user-update',
    description: 'PUT /api/users/:id - Update user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.UPDATE },
    resources: ['users/:id'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'user-delete',
    description: 'DELETE /api/users/:id - Delete user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.DELETE },
    resources: ['users/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'user-upload-avatar',
    description: 'POST /api/users/:id/avatar - Upload user avatar',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS, action: ACTIONS.UPDATE },
    resources: ['users/:id/avatar'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Role API Policies
 */
export const ROLE_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'role-list',
    description: 'GET /api/roles - List all roles',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ROLES, action: ACTIONS.READ },
    resources: ['roles'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'role-view-details',
    description: 'GET /api/roles/:id - View role details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ROLES, action: ACTIONS.READ },
    resources: ['roles/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'role-view-by-name',
    description: 'GET /api/roles/name/:name - View role by name',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ROLES, action: ACTIONS.READ },
    resources: ['roles/name'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Permission API Policies
 */
export const PERMISSION_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'permission-list',
    description: 'GET /api/permissions - List all permissions',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.PERMISSIONS, action: ACTIONS.READ },
    resources: ['permissions'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'permission-view-by-role',
    description: 'GET /api/permissions/role/:roleId - View permissions by role',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.PERMISSIONS, action: ACTIONS.READ },
    resources: ['permissions/role'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'permission-view-by-module',
    description: 'GET /api/permissions/module/:module - View permissions by module',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.PERMISSIONS, action: ACTIONS.READ },
    resources: ['permissions/module'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'permission-view-by-feature',
    description: 'GET /api/permissions/feature/:feature - View permissions by feature',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.PERMISSIONS, action: ACTIONS.READ },
    resources: ['permissions/feature'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'permission-view-by-action',
    description: 'GET /api/permissions/action/:action - View permissions by action',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.PERMISSIONS, action: ACTIONS.READ },
    resources: ['permissions/action'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'permission-check',
    description: 'GET /api/permissions/check/:roleId/:module/:feature/:action - Check permission',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.PERMISSIONS, action: ACTIONS.READ },
    resources: ['permissions/check'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Member Role API Policies
 */
export const MEMBER_ROLE_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'member-role-list',
    description: 'GET /api/member-roles - List all member roles',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.MEMBER_ROLES, action: ACTIONS.READ },
    resources: ['member-roles'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'member-role-view-details',
    description: 'GET /api/member-roles/:id - View member role details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.MEMBER_ROLES, action: ACTIONS.READ },
    resources: ['member-roles/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'member-role-assign',
    description: 'POST /api/member-roles/assign - Assign role to user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.MEMBER_ROLES, action: ACTIONS.CREATE },
    resources: ['member-roles/assign'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'member-role-remove',
    description: 'DELETE /api/member-roles/:id - Remove role from user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.MEMBER_ROLES, action: ACTIONS.DELETE },
    resources: ['member-roles/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'member-role-view-user-roles',
    description: 'GET /api/member-roles/user/:userId - View roles for user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.MEMBER_ROLES, action: ACTIONS.READ },
    resources: ['member-roles/user'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'member-role-view-role-users',
    description: 'GET /api/member-roles/role/:roleId - View users with role',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.MEMBER_ROLES, action: ACTIONS.READ },
    resources: ['member-roles/role'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Face Images API Policies
 */
export const FACE_IMAGES_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'face-images-list',
    description: 'GET /api/face-images - List all face images',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_IMAGES, action: ACTIONS.READ },
    resources: ['face-images'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-images-view-details',
    description: 'GET /api/face-images/:id - View face image details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_IMAGES, action: ACTIONS.READ },
    resources: ['face-images/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-images-view-by-user',
    description: 'GET /api/face-images/user/:userId - View face images by user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_IMAGES, action: ACTIONS.READ },
    resources: ['face-images/user'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-images-view-by-angle',
    description: 'GET /api/face-images/angle/:imageAngle - View face images by angle',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_IMAGES, action: ACTIONS.READ },
    resources: ['face-images/angle'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-images-view-by-date-range',
    description: 'GET /api/face-images/date-range/:startDate/:endDate - View face images by date range',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_IMAGES, action: ACTIONS.READ },
    resources: ['face-images/date-range'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-images-view-by-user-and-angle',
    description: 'GET /api/face-images/user/:userId/angle/:imageAngle - View face images by user and angle',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_IMAGES, action: ACTIONS.READ },
    resources: ['face-images/user/angle'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Face Recognition Logs API Policies
 */
export const FACE_RECOGNITION_LOGS_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'face-recognition-logs-list',
    description: 'GET /api/face-recognition-logs - List all face recognition logs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_RECOGNITION, action: ACTIONS.READ },
    resources: ['face-recognition-logs'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-recognition-logs-view-details',
    description: 'GET /api/face-recognition-logs/:id - View face recognition log details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_RECOGNITION, action: ACTIONS.READ },
    resources: ['face-recognition-logs/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-recognition-logs-create',
    description: 'POST /api/face-recognition-logs - Log recognition event',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_RECOGNITION, action: ACTIONS.CREATE },
    resources: ['face-recognition-logs'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'face-recognition-logs-delete',
    description: 'DELETE /api/face-recognition-logs/:id - Delete recognition log',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_RECOGNITION, action: ACTIONS.DELETE },
    resources: ['face-recognition-logs/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Edge Device API Policies
 */
export const EDGE_DEVICE_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'edge-device-list',
    description: 'GET /api/edge-devices - List all edge devices',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICES, action: ACTIONS.READ },
    resources: ['edge-devices'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-view-details',
    description: 'GET /api/edge-devices/:id - View edge device details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICES, action: ACTIONS.READ },
    resources: ['edge-devices/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Edge Device Info API Policies
 */
export const EDGE_DEVICE_INFO_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'edge-device-info-list',
    description: 'GET /api/edge-device-info - List all edge device info',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_INFO, action: ACTIONS.READ },
    resources: ['edge-device-info'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-info-view-details',
    description: 'GET /api/edge-device-info/:id - View edge device info details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_INFO, action: ACTIONS.READ },
    resources: ['edge-device-info/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Edge Device Logs API Policies
 */
export const EDGE_DEVICE_LOGS_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'edge-device-logs-list',
    description: 'GET /api/edge-device-logs - List all edge device logs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.READ },
    resources: ['edge-device-logs'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-logs-view-details',
    description: 'GET /api/edge-device-logs/:id - View edge device log details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.READ },
    resources: ['edge-device-logs/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-logs-create',
    description: 'POST /api/edge-device-logs - Create edge device log',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.CREATE },
    resources: ['edge-device-logs'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-logs-delete',
    description: 'DELETE /api/edge-device-logs/:id - Delete edge device log',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.DELETE },
    resources: ['edge-device-logs/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-logs-view-by-device',
    description: 'GET /api/edge-device-logs/device/:edgeDeviceId - View logs by device',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.READ },
    resources: ['edge-device-logs/device'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-logs-view-by-level',
    description: 'GET /api/edge-device-logs/level/:logLevel - View logs by level',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.READ },
    resources: ['edge-device-logs/level'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-logs-view-by-date-range',
    description: 'GET /api/edge-device-logs/date-range/:startDate/:endDate - View logs by date range',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.READ },
    resources: ['edge-device-logs/date-range'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'edge-device-logs-view-by-device-and-level',
    description: 'GET /api/edge-device-logs/device/:edgeDeviceId/level/:logLevel - View logs by device and level',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS, action: ACTIONS.READ },
    resources: ['edge-device-logs/device/level'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Daily Attendance Summaries API Policies
 */
export const ATTENDANCE_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'attendance-list',
    description: 'GET /api/attendance-summaries - List all attendance summaries',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ATTENDANCE, action: ACTIONS.READ },
    resources: ['attendance-summaries'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'attendance-view-details',
    description: 'GET /api/attendance-summaries/:id - View attendance summary details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ATTENDANCE, action: ACTIONS.READ },
    resources: ['attendance-summaries/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'attendance-view-by-user',
    description: 'GET /api/attendance-summaries/user/:userId - View attendance by user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ATTENDANCE, action: ACTIONS.READ },
    resources: ['attendance-summaries/user'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'attendance-view-by-shift',
    description: 'GET /api/attendance-summaries/shift/:shiftId - View attendance by shift',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ATTENDANCE, action: ACTIONS.READ },
    resources: ['attendance-summaries/shift'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'attendance-view-by-date',
    description: 'GET /api/attendance-summaries/date/:workDate - View attendance by date',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ATTENDANCE, action: ACTIONS.READ },
    resources: ['attendance-summaries/date'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'attendance-view-by-date-range',
    description: 'GET /api/attendance-summaries/user/:userId/date-range/:startDate/:endDate - View attendance by date range',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ATTENDANCE, action: ACTIONS.READ },
    resources: ['attendance-summaries/user/date-range'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Policy API Policies
 */
export const POLICY_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'policy-list',
    description: 'GET /api/policies - List all policies',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.POLICIES, action: ACTIONS.READ },
    resources: ['policies'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'policy-view-details',
    description: 'GET /api/policies/:id - View policy details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.POLICIES, action: ACTIONS.READ },
    resources: ['policies/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'policy-create',
    description: 'POST /api/policies - Create new policy',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.POLICIES, action: ACTIONS.CREATE },
    resources: ['policies'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'policy-update',
    description: 'PUT /api/policies/:id - Update policy',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.POLICIES, action: ACTIONS.UPDATE },
    resources: ['policies/:id'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'policy-delete',
    description: 'DELETE /api/policies/:id - Delete policy',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.POLICIES, action: ACTIONS.DELETE },
    resources: ['policies/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'policy-view-for-user',
    description: 'GET /api/policies/user/:userId - View policies for user',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.POLICIES, action: ACTIONS.READ },
    resources: ['policies/user'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'policy-evaluate',
    description: 'POST /api/policies/evaluate - Evaluate policy',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.POLICIES, action: ACTIONS.READ },
    resources: ['policies/evaluate'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Identity API Policies
 */
export const IDENTITY_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'identity-login',
    description: 'POST /api/identity/login - User login',
    type: POLICY_TYPES.CUSTOM,
    conditions: { public: true },
    resources: ['identity/login'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-logout',
    description: 'POST /api/identity/logout - User logout',
    type: POLICY_TYPES.CUSTOM,
    conditions: { authenticated: true },
    resources: ['identity/logout'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-refresh-token',
    description: 'POST /api/identity/refresh-token - Refresh access token',
    type: POLICY_TYPES.CUSTOM,
    conditions: { authenticated: true },
    resources: ['identity/refresh-token'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-verify-token',
    description: 'POST /api/identity/verify-token - Verify token',
    type: POLICY_TYPES.CUSTOM,
    conditions: { public: true },
    resources: ['identity/verify-token'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-me',
    description: 'GET /api/identity/me - Get current user profile',
    type: POLICY_TYPES.CUSTOM,
    conditions: { authenticated: true },
    resources: ['identity/me'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-profile',
    description: 'GET /api/identity/me - Get current user profile',
    type: POLICY_TYPES.CUSTOM,
    conditions: { authenticated: true },
    resources: ['identity/me'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-update-profile',
    description: 'PUT /api/identity/me - Update current user profile',
    type: POLICY_TYPES.CUSTOM,
    conditions: { authenticated: true, ownership: true },
    resources: ['identity/me'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-sessions',
    description: 'GET /api/identity/sessions - Get user sessions',
    type: POLICY_TYPES.CUSTOM,
    conditions: { authenticated: true },
    resources: ['identity/sessions'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
  {
    name: 'identity-revoke-session',
    description: 'POST /api/identity/sessions/revoke - Revoke user session',
    type: POLICY_TYPES.CUSTOM,
    conditions: { authenticated: true },
    resources: ['identity/sessions/revoke'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },
];

/**
 * Storage API Policies
 */
export const STORAGE_API_POLICIES: PolicyConfiguration[] = [
  // Bucket Management Policies
  {
    name: 'storage-bucket-list',
    description: 'GET /api/storage/buckets - List all buckets',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.READ },
    resources: ['storage/buckets'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'storage-bucket-view-details',
    description: 'GET /api/storage/buckets/:id - View bucket details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.READ },
    resources: ['storage/buckets/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'storage-bucket-create',
    description: 'POST /api/storage/buckets - Create new bucket',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.CREATE },
    resources: ['storage/buckets'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'storage-bucket-update',
    description: 'PUT /api/storage/buckets/:id - Update bucket',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.UPDATE },
    resources: ['storage/buckets/:id'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'storage-bucket-delete',
    description: 'DELETE /api/storage/buckets/:id - Delete bucket',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.DELETE },
    resources: ['storage/buckets/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  // File Management Policies
  {
    name: 'storage-file-upload-url',
    description: 'POST /api/storage/buckets/:bucketId/upload-url - Generate upload URL',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.CREATE },
    resources: ['storage/buckets/upload-url'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'storage-file-download-url',
    description: 'POST /api/storage/buckets/:bucketId/download-url - Generate download URL',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.READ },
    resources: ['storage/buckets/download-url'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'storage-file-stream',
    description: 'GET /api/storage/files/:fileId/stream - Stream file by ID',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.STORAGE, action: ACTIONS.READ },
    resources: ['storage/files/:fileId/stream'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Tenant API Policies
 */
export const TENANT_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'tenant-list',
    description: 'GET /api/tenants - List all tenants',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS, action: ACTIONS.READ },
    resources: ['tenants'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'tenant-view-details',
    description: 'GET /api/tenants/:id - View tenant details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS, action: ACTIONS.READ },
    resources: ['tenants/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'tenant-create',
    description: 'POST /api/tenants - Create new tenant',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS, action: ACTIONS.CREATE },
    resources: ['tenants'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'tenant-update',
    description: 'PUT /api/tenants/:id - Update tenant',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS, action: ACTIONS.UPDATE },
    resources: ['tenants/:id'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'tenant-delete',
    description: 'DELETE /api/tenants/:id - Delete tenant',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS, action: ACTIONS.DELETE },
    resources: ['tenants/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'tenant-view-by-name',
    description: 'GET /api/tenants/name/:name - View tenant by name',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS, action: ACTIONS.READ },
    resources: ['tenants/name'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'tenant-view-by-status',
    description: 'GET /api/tenants/status/:status - View tenants by status',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS, action: ACTIONS.READ },
    resources: ['tenants/status'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Shift API Policies
 */
export const SHIFT_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'shift-list',
    description: 'GET /api/shifts - List all shifts',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFTS, action: ACTIONS.READ },
    resources: ['shifts'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-view-details',
    description: 'GET /api/shifts/:id - View shift details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFTS, action: ACTIONS.READ },
    resources: ['shifts/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-create',
    description: 'POST /api/shifts - Create new shift',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFTS, action: ACTIONS.CREATE },
    resources: ['shifts'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-create-with-detail',
    description: 'POST /api/shifts/with-detail - Create shift with detail',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFTS, action: ACTIONS.CREATE },
    resources: ['shifts/with-detail'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-update',
    description: 'PUT /api/shifts/:id - Update shift',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFTS, action: ACTIONS.UPDATE },
    resources: ['shifts/:id'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-delete',
    description: 'DELETE /api/shifts/:id - Delete shift',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFTS, action: ACTIONS.DELETE },
    resources: ['shifts/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * Shift Detail API Policies
 */
export const SHIFT_DETAIL_API_POLICIES: PolicyConfiguration[] = [
  {
    name: 'shift-detail-list',
    description: 'GET /api/shift-details - List all shift details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFT_DETAILS, action: ACTIONS.READ },
    resources: ['shift-details'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-detail-view-details',
    description: 'GET /api/shift-details/:id - View shift detail details',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFT_DETAILS, action: ACTIONS.READ },
    resources: ['shift-details/:id'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-detail-create',
    description: 'POST /api/shift-details - Create new shift detail',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFT_DETAILS, action: ACTIONS.CREATE },
    resources: ['shift-details'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-detail-update',
    description: 'PUT /api/shift-details/:id - Update shift detail',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFT_DETAILS, action: ACTIONS.UPDATE },
    resources: ['shift-details/:id'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
  {
    name: 'shift-detail-delete',
    description: 'DELETE /api/shift-details/:id - Delete shift detail',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFT_DETAILS, action: ACTIONS.DELETE },
    resources: ['shift-details/:id'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 31,
    is_active: true,
  },
];

/**
 * All API Policies Combined
 */
export const ALL_API_POLICIES: PolicyConfiguration[] = [
  ...CAMERA_API_POLICIES,
  ...USER_API_POLICIES,
  ...ROLE_API_POLICIES,
  ...PERMISSION_API_POLICIES,
  ...MEMBER_ROLE_API_POLICIES,
  ...FACE_IMAGES_API_POLICIES,
  ...FACE_RECOGNITION_LOGS_API_POLICIES,
  ...EDGE_DEVICE_API_POLICIES,
  ...EDGE_DEVICE_INFO_API_POLICIES,
  ...EDGE_DEVICE_LOGS_API_POLICIES,
  ...ATTENDANCE_API_POLICIES,
  ...SHIFT_API_POLICIES,
  ...SHIFT_DETAIL_API_POLICIES,
  ...POLICY_API_POLICIES,
  ...IDENTITY_API_POLICIES,
  ...STORAGE_API_POLICIES,
  ...TENANT_API_POLICIES,
];
