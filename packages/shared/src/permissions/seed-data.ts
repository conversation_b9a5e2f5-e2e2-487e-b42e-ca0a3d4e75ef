/**
 * Shared seed data for permissions and policies
 * Used by both API and Web applications
 */

import {
  SYSTEM_ROLES,
  MODULES,
  ACTIONS,
  FEATURES,
  RESOURCE_TYPES,
  POLICY_TYPES,
  POLICY_EFFECTS,
} from './constants.js';
import type {
  PermissionMatrix,
  PolicyConfiguration,
  SeedConfiguration,
} from './types.js';
import { ALL_API_POLICIES } from './api-policies.js';

/**
 * Default system roles configuration
 */
export const DEFAULT_ROLES = [
  {
    name: SYSTEM_ROLES.SUPER_ADMIN,
    description: 'Super Administrator with full system access',
    is_system: true,
  },
  {
    name: SYSTEM_ROLES.ADMIN,
    description: 'Administrator with management access',
    is_system: true,
  },
  {
    name: SYSTEM_ROLES.MANAGER,
    description: 'Manager with limited management access',
    is_system: true,
  },
  {
    name: SYSTEM_ROLES.USER,
    description: 'Regular user with basic access',
    is_system: true,
  },
  {
    name: SYSTEM_ROLES.VIEWER,
    description: 'Viewer with read-only access',
    is_system: true,
  },
];

/**
 * Permission matrix defining what each role can do
 */
export const PERMISSION_MATRIX: PermissionMatrix = {
  [SYSTEM_ROLES.SUPER_ADMIN]: [
    // Super Admin has all permissions for all modules
    ...Object.values(MODULES).flatMap((module) =>
      Object.values(ACTIONS).map((action) => ({
        module,
        feature: '*',
        action,
        resource_type: RESOURCE_TYPES.ALL,
      })),
    ),
  ],

  [SYSTEM_ROLES.ADMIN]: [
    // User Management
    { module: MODULES.USERS, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.ROLES, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.PERMISSIONS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.ALL },
    { module: MODULES.MEMBER_ROLES, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },

    // Organization Management
    { module: MODULES.TENANTS, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.UNITS, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },

    // Camera & Device Management
    { module: MODULES.CAMERAS, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.EDGE_DEVICES, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.EDGE_DEVICE_INFO, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.EDGE_DEVICE_LOGS, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },

    // Face Recognition
    { module: MODULES.FACE_RECOGNITION, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.FACE_IMAGES, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },

    // Attendance & Shifts
    { module: MODULES.ATTENDANCE, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.SHIFTS, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.SHIFT_DETAILS, feature: '*', action: ACTIONS.MANAGE, resource_type: RESOURCE_TYPES.ORGANIZATION },

    // Reports & Dashboard
    { module: MODULES.REPORTS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.DASHBOARD, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.ORGANIZATION },

    // Settings
    { module: MODULES.SETTINGS, feature: 'organization', action: ACTIONS.UPDATE, resource_type: RESOURCE_TYPES.ORGANIZATION },
    { module: MODULES.POLICIES, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.ORGANIZATION },
  ],

  [SYSTEM_ROLES.MANAGER]: [
    // User Management (Limited)
    { module: MODULES.USERS, feature: 'profile', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.USERS, feature: 'profile', action: ACTIONS.UPDATE, resource_type: RESOURCE_TYPES.OWN },

    // Organization (Read-only)
    { module: MODULES.UNITS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },

    // Camera & Device (Read-only)
    { module: MODULES.CAMERAS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.EDGE_DEVICES, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.EDGE_DEVICE_INFO, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.EDGE_DEVICE_LOGS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },

    // Face Recognition (Read-only)
    { module: MODULES.FACE_RECOGNITION, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.FACE_IMAGES, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },

    // Attendance & Shifts
    { module: MODULES.ATTENDANCE, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.SHIFTS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.SHIFT_DETAILS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },

    // Reports & Dashboard
    { module: MODULES.REPORTS, feature: '*', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.DASHBOARD, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
  ],

  [SYSTEM_ROLES.USER]: [
    // User Management (Own profile only)
    { module: MODULES.USERS, feature: 'profile', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.OWN },
    { module: MODULES.USERS, feature: 'profile', action: ACTIONS.UPDATE, resource_type: RESOURCE_TYPES.OWN },

    // Attendance (Own data only)
    { module: MODULES.ATTENDANCE, feature: 'own', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.OWN },
    { module: MODULES.FACE_RECOGNITION, feature: 'own', action: ACTIONS.READ, resource_type: RESOURCE_TYPES.OWN },

    // Dashboard (Limited view)
    { module: MODULES.DASHBOARD, feature: 'personal', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.OWN },

    // Settings (Account only)
    { module: MODULES.SETTINGS, feature: 'account', action: ACTIONS.UPDATE, resource_type: RESOURCE_TYPES.OWN },
  ],

  [SYSTEM_ROLES.VIEWER]: [
    // View-only permissions
    { module: MODULES.ATTENDANCE, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.REPORTS, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.CAMERAS, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.FACE_RECOGNITION, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.FACE_IMAGES, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.EDGE_DEVICES, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.EDGE_DEVICE_INFO, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.EDGE_DEVICE_LOGS, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.SHIFTS, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.SHIFT_DETAILS, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.UNITS, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
    { module: MODULES.DASHBOARD, feature: '*', action: ACTIONS.VIEW, resource_type: RESOURCE_TYPES.TEAM },
  ],
};

/**
 * Default policies configuration
 */
export const DEFAULT_POLICIES: PolicyConfiguration[] = [
  // Include all detailed API policies
  ...ALL_API_POLICIES,

  // System-level policies
  {
    name: 'Super Admin Full Access',
    description: 'Grants full system access to super administrators',
    type: POLICY_TYPES.ROLE,
    conditions: { role: SYSTEM_ROLES.SUPER_ADMIN },
    resources: ['*'],
    actions: [ACTIONS.CREATE, ACTIONS.READ, ACTIONS.UPDATE, ACTIONS.DELETE, ACTIONS.MANAGE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 1,
    is_active: true,
  },

  {
    name: 'Admin Management Access',
    description: 'Grants management access to administrators',
    type: POLICY_TYPES.ROLE,
    conditions: { role: SYSTEM_ROLES.ADMIN },
    resources: ['users', 'roles', 'cameras', 'attendance', 'reports', 'edge-devices', 'face-recognition', 'shifts', 'units', 'tenants'],
    actions: [ACTIONS.CREATE, ACTIONS.READ, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 10,
    is_active: true,
  },

  // API-specific policies for PBAC
  {
    name: 'camera-access',
    description: 'Access to camera management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS },
    resources: ['cameras'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'camera-read',
    description: 'Read access to cameras',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.READ },
    resources: ['cameras'],
    actions: [ACTIONS.READ],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 30,
    is_active: true,
  },

  {
    name: 'camera-create',
    description: 'Create access to cameras',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.CREATE },
    resources: ['cameras'],
    actions: [ACTIONS.CREATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 30,
    is_active: true,
  },

  {
    name: 'camera-update',
    description: 'Update access to cameras',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.UPDATE },
    resources: ['cameras'],
    actions: [ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 30,
    is_active: true,
  },

  {
    name: 'camera-delete',
    description: 'Delete access to cameras',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.CAMERAS, action: ACTIONS.DELETE },
    resources: ['cameras'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 30,
    is_active: true,
  },

  // User Management Policies
  {
    name: 'user-access',
    description: 'Access to user management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.USERS },
    resources: ['users'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'role-access',
    description: 'Access to role management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ROLES },
    resources: ['roles'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'permission-access',
    description: 'Access to permission management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.PERMISSIONS },
    resources: ['permissions'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  // Organization Management Policies
  {
    name: 'tenant-access',
    description: 'Access to tenant management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.TENANTS },
    resources: ['tenants'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'unit-access',
    description: 'Access to unit management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.UNITS },
    resources: ['units'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  // Edge Device Policies
  {
    name: 'edge-device-access',
    description: 'Access to edge device management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICES },
    resources: ['edge-devices'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'edge-device-info-access',
    description: 'Access to edge device info APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_INFO },
    resources: ['edge-device-info'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'edge-device-logs-access',
    description: 'Access to edge device logs APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.EDGE_DEVICE_LOGS },
    resources: ['edge-device-logs'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  // Face Recognition Policies
  {
    name: 'face-images-access',
    description: 'Access to face images APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_IMAGES },
    resources: ['face-images'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'face-recognition-logs-access',
    description: 'Access to face recognition logs APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.FACE_RECOGNITION },
    resources: ['face-recognition-logs'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  // Attendance & Shift Policies
  {
    name: 'attendance-access',
    description: 'Access to attendance summaries APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.ATTENDANCE },
    resources: ['attendance-summaries'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'shift-access',
    description: 'Access to shift management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFTS },
    resources: ['shifts'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'shift-detail-access',
    description: 'Access to shift detail APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.SHIFT_DETAILS },
    resources: ['shift-details'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.UPDATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  {
    name: 'member-role-access',
    description: 'Access to member role management APIs',
    type: POLICY_TYPES.PERMISSION,
    conditions: { module: MODULES.MEMBER_ROLES },
    resources: ['member-roles'],
    actions: [ACTIONS.READ, ACTIONS.CREATE, ACTIONS.DELETE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 20,
    is_active: true,
  },

  // Special Policies
  {
    name: 'User Self Management',
    description: 'Allows users to manage their own profile',
    type: POLICY_TYPES.RESOURCE,
    conditions: { ownership: true },
    resources: ['users/profile'],
    actions: [ACTIONS.READ, ACTIONS.UPDATE],
    effect: POLICY_EFFECTS.ALLOW,
    priority: 50,
    is_active: true,
  },

  // Note: All detailed API endpoint policies are now included via ALL_API_POLICIES above
  // This eliminates duplicate policies and ensures consistency

  {
    name: 'Deny Sensitive Operations',
    description: 'Denies access to sensitive system operations',
    type: POLICY_TYPES.CUSTOM,
    conditions: { sensitive: true },
    resources: ['system/*', 'database/*'],
    actions: [ACTIONS.DELETE],
    effect: POLICY_EFFECTS.DENY,
    priority: 1,
    is_active: true,
  },
];

/**
 * Complete seed configuration
 */
export const SEED_CONFIGURATION: SeedConfiguration = {
  roles: DEFAULT_ROLES,
  permissions: PERMISSION_MATRIX,
  policies: DEFAULT_POLICIES,
  default_admin: {
    username: 'admin',
    password: 'admin123',
    email: '<EMAIL>',
    name: 'System Administrator',
  },
};

/**
 * Default shifts configuration
 * Note: tenant_id, code, and created_by will be set dynamically during seeding
 */
export const DEFAULT_SHIFTS = [
  { name: 'Day Shift', type: 'Fixed', work_coefficient: 1.0 },
  { name: 'Night Shift', type: 'Fixed', work_coefficient: 1.2 },
  { name: 'Weekend Shift', type: 'Flexible', work_coefficient: 1.5 },
];

/**
 * Default tenant configuration
 */
export const DEFAULT_TENANT = {
  name: 'Default Organization',
  address: 'System Default Address',
};

/**
 * Default unit configuration
 */
export const DEFAULT_UNIT = {
  name: 'Main Unit',
  organization_id: 'default-org',
};

/**
 * System constants for seeding
 */
export const SYSTEM_CONSTANTS = {
  SYSTEM_USER_ID: 'system',
  DEFAULT_TENANT,
  DEFAULT_UNIT,
  ROLES: SYSTEM_ROLES,
  MODULES,
  ACTIONS,
  FEATURES,
  RESOURCE_TYPES,
  POLICY_TYPES,
  POLICY_EFFECTS,
  SHIFTS: DEFAULT_SHIFTS,
};
