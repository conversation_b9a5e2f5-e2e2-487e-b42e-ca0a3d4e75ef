import { Status } from './status.types';

/**
 * Work Shift Type Enum
 */
export enum WorkShiftType {
  Administrative = 'Administrative', // hành chính
  Shift = 'Shift' // ca kíp
}

/**
 * Display labels for WorkShiftType enum
 */
export const WorkShiftTypeDisplay = {
  [WorkShiftType.Administrative]: 'Hành chính',
  [WorkShiftType.Shift]: 'Ca kíp'
};

/**
 * Entity Type Enum
 */
export enum EntityType {
  All = 'All',
  Custom = 'Custom'
}

/**
 * Display labels for EntityType enum
 */
export const EntityTypeDisplay = {
  [EntityType.All]: 'Tất cả',
  [EntityType.Custom]: 'Tùy chỉnh'
};

/**
 * Shift Assignment Type Enum
 */
export enum ShiftAssignmentType {
  Weekly = 'Weekly',
  FixedDay = 'FixedDay'
}

/**
 * Display labels for ShiftAssignmentType enum
 */
export const ShiftAssignmentTypeDisplay = {
  [ShiftAssignmentType.Weekly]: '<PERSON> tuần',
  [ShiftAssignmentType.FixedDay]: 'Ngày cố định'
};

/**
 * Date Range Type Enum
 */
export enum DateRangeType {
  AboutDays = 'AboutDays',
  InfinityDay = 'InfinityDay'
}

/**
 * Display labels for DateRangeType enum
 */
export const DateRangeTypeDisplay = {
  [DateRangeType.AboutDays]: 'Khoảng ngày',
  [DateRangeType.InfinityDay]: 'Vô hạn'
};

/**
 * Shift Detail Attributes Interface
 * Defines the core data structure for shift details
 */
export interface ShiftDetailAttributes {
  _id: string;
  tenant_id: string;
  shift_start?: Date; // Thời gian bắt đầu ca làm việc
  clock_in_start?: Date; // Thời gian bắt đầu cho phép chấm công
  allow_late: boolean; // Cho phép đến muộn
  late_cutoff: boolean; // đến sau quá số phút sẽ không ghi nhận nửa ca đầu
  late_allowance?: number; // Số phút cho phép đến muộn
  late_threshold?: number; // Số phút vào sau sẽ không ghi nhận nửa ca đầu
  has_break: boolean; // Nghỉ giữa giờ
  break_start?: Date; // Thời gian bắt đầu nghỉ giữa giờ
  break_end?: Date; // Thời gian kết thúc nghỉ giữa giờ
  shift_end?: Date; // Thời gian kết thúc ca
  clock_out_latest?: Date; // Thời gian muộn nhất cho phép chấm công ra
  allow_early_out: boolean; // Cho phép chấm công ra trước
  early_threshold?: number; // Số phút chấm công ra trước quy định bị tính là về sớm
  early_no_half_shift: boolean; // Cho phép không ghi nhận nửa công ca sau nếu về sớm
  early_cutoff?: number; // Số phút cho phép về sớm nếu vượt quá sẽ không được tính công nửa ca sau
  worked_hours?: number; // Số giờ làm việc
  clock_in_at_start: boolean; // yêu cầu chấm công
  late_threshold_all_shift: number; // Số phút cho phép đến muộn (quá không ghi nhận công cho toàn bộ ca làm việc)
}
