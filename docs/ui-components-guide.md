# UI Components Guide

## Hướng dẫn sử dụng UI Components trong dự án C-CAM

### Tổng quan

Dự án sử dụng một bộ UI components được xây dựng dựa trên Radix UI và Tailwind CSS, nằm trong thư mục `apps/web/src/components/ui`. Khi phát triển component mới, **luôn ưu tiên sử dụng các component có sẵn** thay vì tự tạo từ đầu.

### Nguyên tắc sử dụng

1. **Luôn kiểm tra UI components có sẵn trước khi tạo mới**
2. **Sử dụng shared components khi có thể**
3. **Giữ nguyên giao diện gốc khi refactor**
4. **Tổ chức code theo cấu trúc rõ ràng**

### Danh sách UI Components chính

#### Layout & Structure
- `Card`, `<PERSON><PERSON><PERSON>er`, `Card<PERSON>itle`, `<PERSON><PERSON>ontent`, `<PERSON><PERSON>ooter` - <PERSON><PERSON> chức nội dung
- `Separator` - <PERSON><PERSON> chia các section
- `Tabs`, `TabsList`, `TabsTrigger`, `TabsContent` - Tab navigation
- `Dialog`, `DialogContent`, `DialogHeader`, `DialogTitle` - Modal dialogs
- `Sheet` - Side panels
- `Collapsible` - Nội dung có thể thu gọn

#### Form Controls
- `Input` - Text input fields
- `Textarea` - Multi-line text input
- `Button` - Buttons với các variants
- `Checkbox` - Checkboxes
- `RadioGroup`, `RadioGroupItem` - Radio buttons
- `Switch` - Toggle switches
- `Select` - Dropdown selects
- `Label` - Form labels
- `Form` - Form wrapper với validation

#### Navigation
- `NavigationMenu` - Main navigation
- `Breadcrumb` - Breadcrumb navigation
- `Pagination` - Page navigation
- `Menubar` - Menu bars
- `DropdownMenu` - Dropdown menus
- `ContextMenu` - Context menus

#### Feedback & Display
- `Alert`, `AlertDialog` - Alerts và confirmations
- `Badge` - Status badges
- `Progress` - Progress bars
- `Skeleton` - Loading skeletons
- `Tooltip` - Tooltips
- `HoverCard` - Hover cards
- `Popover` - Popovers

#### Data Display
- `Table` - Data tables
- `Calendar` - Date picker
- `Chart` - Charts và graphs
- `Avatar` - User avatars
- `Carousel` - Image carousels

### Ví dụ thực tế: Refactor CreateShiftPopup

#### Trước khi refactor:
```tsx
// Sử dụng div và styling thủ công
<div className="space-y-6">
  <div className="space-y-2">
    <Label>Tên ca làm việc</Label>
    <Input />
  </div>
  <div className="border-t pt-4">
    // Fixed shift config
  </div>
</div>
```

#### Sau khi refactor:
```tsx
// Sử dụng Card và Tabs components
<Card>
  <CardHeader>
    <CardTitle>Thông tin cơ bản</CardTitle>
  </CardHeader>
  <CardContent>
    <Label>Tên ca làm việc</Label>
    <Input />
  </CardContent>
</Card>

<Card>
  <CardHeader>
    <CardTitle>Cấu hình ca làm việc</CardTitle>
  </CardHeader>
  <CardContent>
    <Tabs value={type} onValueChange={setType}>
      <TabsList>
        <TabsTrigger value="Fixed">Cố định</TabsTrigger>
        <TabsTrigger value="Flexible">Linh hoạt</TabsTrigger>
      </TabsList>
      <TabsContent value="Fixed">
        // Fixed shift config
      </TabsContent>
    </Tabs>
  </CardContent>
</Card>
```

### Lợi ích của việc sử dụng UI Components

1. **Consistency**: Giao diện nhất quán trong toàn bộ ứng dụng
2. **Accessibility**: Các component đã được tối ưu cho accessibility
3. **Maintainability**: Dễ bảo trì và cập nhật
4. **Performance**: Tối ưu hóa performance
5. **Developer Experience**: API rõ ràng, dễ sử dụng

### Best Practices

#### 1. Tổ chức Layout với Card
```tsx
// ✅ Good: Sử dụng Card để nhóm nội dung liên quan
<Card>
  <CardHeader>
    <CardTitle>Tiêu đề section</CardTitle>
  </CardHeader>
  <CardContent>
    // Nội dung chính
  </CardContent>
</Card>

// ❌ Bad: Sử dụng div thông thường
<div className="border rounded p-4">
  <h3>Tiêu đề section</h3>
  // Nội dung
</div>
```

#### 2. Phân chia nội dung với Separator
```tsx
// ✅ Good: Sử dụng Separator
<div>
  <Section1 />
  <Separator />
  <Section2 />
</div>

// ❌ Bad: Sử dụng border thủ công
<div>
  <Section1 />
  <div className="border-t my-4" />
  <Section2 />
</div>
```

#### 3. Navigation với Tabs
```tsx
// ✅ Good: Sử dụng Tabs cho navigation
<Tabs value={activeTab} onValueChange={setActiveTab}>
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
    <TabsTrigger value="tab2">Tab 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Content 1</TabsContent>
  <TabsContent value="tab2">Content 2</TabsContent>
</Tabs>

// ❌ Bad: Tự implement tab logic
<div>
  <div className="flex border-b">
    <button onClick={() => setTab('tab1')}>Tab 1</button>
    <button onClick={() => setTab('tab2')}>Tab 2</button>
  </div>
  {activeTab === 'tab1' && <div>Content 1</div>}
  {activeTab === 'tab2' && <div>Content 2</div>}
</div>
```

### Checklist khi tạo component mới

- [ ] Đã kiểm tra các UI components có sẵn?
- [ ] Có thể sử dụng Card để tổ chức layout?
- [ ] Có thể sử dụng Separator để phân chia sections?
- [ ] Có thể sử dụng Tabs cho navigation?
- [ ] Đã sử dụng Form components cho form inputs?
- [ ] Giao diện có nhất quán với design system?
- [ ] Component có accessible không?
- [ ] Code có clean và maintainable không?

### Kết luận

Việc sử dụng đúng UI components không chỉ giúp code clean hơn mà còn đảm bảo tính nhất quán và accessibility của ứng dụng. Luôn ưu tiên sử dụng các component có sẵn và chỉ tạo mới khi thực sự cần thiết.
