# Create Tenant với Auto-Switch

## 🎯 Tổng quan

Khi tạo tổ chức mới, hệ thống sẽ tự động chuyển đổi sang tổ chức đó và cập nhật access token với `tenant_id` mới thông qua refresh token API.

## 🔄 Luồng hoạt động

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Database
    
    User->>Frontend: Tạo tổ chức mới
    Frontend->>API: POST /api/tenants {name, address}
    API->>Database: Create new tenant
    API->>Frontend: Return new tenant data
    
    Note over Frontend: Auto-switch logic
    Frontend->>API: POST /api/identity/refresh-token {tenantId: newTenantId}
    API->>Database: Verify refresh token
    API->>API: Create new token with new tenantId
    API->>Frontend: Return new access_token with tenant_id
    
    Frontend->>Frontend: Update access_token in storage
    Frontend->>Frontend: Update selected tenant
    Frontend->>Frontend: Invalidate all queries
    Frontend->>User: <PERSON><PERSON><PERSON> thị "Đã tạo và chuyển đổi thành công"
```

## 🛠️ Implementation

### 1. **Updated handleCreateTenant**

```typescript
const handleCreateTenant = useCallback(
  async (data: CreateTenantRequest) => {
    try {
      const response = await createTenantMutation.mutateAsync(data)
      
      // Auto-switch to the newly created tenant
      const newTenant = response.organization
      if (newTenant?.id) {
        console.log('Auto-switching to newly created tenant:', newTenant.name)
        
        // Return the new tenant ID so the caller can handle the switch
        return {
          success: true,
          tenant: newTenant,
          shouldAutoSwitch: true
        }
      }
      
      return { success: true, tenant: newTenant, shouldAutoSwitch: false }
    } catch (error) {
      console.error('Failed to create tenant:', error)
      return { success: false, error }
    }
  },
  [createTenantMutation],
)
```

### 2. **New handleCreateTenantWithAutoSwitch**

```typescript
const handleCreateTenantWithAutoSwitch = useCallback(
  async (data: CreateTenantRequest) => {
    try {
      await withLoading(
        async () => {
          const result = await createTenant(data)
          
          if (result.success && result.shouldAutoSwitch && result.tenant) {
            console.log('Auto-switching to newly created tenant:', result.tenant.name)
            
            // Call refresh token to switch to the new tenant
            const refreshResult = await refreshAccessToken(result.tenant.id)
            
            if (refreshResult?.tenant_switched) {
              // Update persistent storage with the new tenant
              setTenant({
                _id: result.tenant._id,
                id: result.tenant.id,
                name: result.tenant.name,
                address: result.tenant.address,
                created_by: result.tenant.created_by,
                createdAt: result.tenant.createdAt || result.tenant.created_at,
                updatedAt: result.tenant.updatedAt || result.tenant.updated_at,
              })
              
              toast.success(`Đã tạo và chuyển đổi sang tổ chức "${result.tenant.name}" thành công!`)
            } else {
              toast.success('Tổ chức đã được tạo thành công')
            }
          } else if (result.success) {
            toast.success('Tổ chức đã được tạo thành công')
          } else {
            throw new Error('Failed to create tenant')
          }
        },
        {
          message: 'Đang tạo tổ chức mới...',
          priority: 5, // High priority for tenant creation
        },
      )
    } catch (error) {
      console.error('Failed to create tenant with auto-switch:', error)
      const message = error instanceof Error ? error.message : 'Không thể tạo tổ chức'
      toast.error(message)
    }
  },
  [createTenant, refreshAccessToken, setTenant, withLoading],
)
```

## 🧪 Cách sử dụng

### 1. **Trong Component**

```typescript
import { useTenantSelection } from '@/hooks/use-tenant-actions'

function CreateTenantForm() {
  const { handleCreateTenantWithAutoSwitch } = useTenantSelection()
  
  const handleSubmit = async (formData) => {
    await handleCreateTenantWithAutoSwitch({
      name: formData.name,
      address: formData.address
    })
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  )
}
```

### 2. **Hoặc sử dụng handleCreateTenant + handleTenantChange riêng biệt**

```typescript
import { useTenantActions, useTenantSelection } from '@/hooks/use-tenant-actions'

function CreateTenantForm() {
  const { handleCreateTenant } = useTenantActions()
  const { handleTenantChange } = useTenantSelection()
  
  const handleSubmit = async (formData) => {
    const result = await handleCreateTenant({
      name: formData.name,
      address: formData.address
    })
    
    if (result.success && result.shouldAutoSwitch && result.tenant) {
      await handleTenantChange(result.tenant.id)
    }
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  )
}
```

## ✨ Tính năng

### 1. **Seamless UX**
- Tạo tổ chức và chuyển đổi trong một flow duy nhất
- Loading state với message "Đang tạo tổ chức mới..."
- Success toast: "Đã tạo và chuyển đổi sang tổ chức [name] thành công!"

### 2. **Error Handling**
- Comprehensive error handling cho cả create và switch
- Fallback toast nếu create thành công nhưng switch thất bại
- Proper error logging

### 3. **State Management**
- Update access token với tenant_id mới
- Update persistent tenant selection
- Invalidate tất cả queries để refetch với token mới

### 4. **Security**
- Sử dụng refresh token để switch tenant
- Validate tenant access permissions
- Log tenant switch activities

## 🎯 Lợi ích

✅ **Immediate Context**: User ngay lập tức làm việc trong context của tổ chức mới
✅ **Unified Flow**: Tạo và switch trong một action duy nhất
✅ **Smooth UX**: Không cần manual switch sau khi tạo
✅ **Consistent State**: Token và UI state được sync hoàn hảo
✅ **Error Recovery**: Graceful handling nếu auto-switch thất bại

## 🔧 API Response

### Create Tenant Response
```json
{
  "success": true,
  "organization": {
    "_id": "507f1f77bcf86cd799439011",
    "id": "new_tenant_id",
    "name": "New Organization",
    "address": "123 Main St",
    "created_by": "user_id",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### Refresh Token Response (for auto-switch)
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200,
    "token_type": "Bearer",
    "tenant_id": "new_tenant_id",
    "tenant_switched": true
  }
}
```

## 🧪 Testing

```bash
# Test create tenant with auto-switch
curl -X POST http://localhost:3000/api/tenants \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <access_token>" \
  -d '{"name": "Test Org", "address": "123 Test St"}'

# Verify token refresh with new tenant
curl -X POST http://localhost:3000/api/identity/refresh-token \
  -H "Content-Type: application/json" \
  -H "Cookie: refresh_token=<signed_token>" \
  -d '{"tenantId": "new_tenant_id"}'
```
