# Shift Detail API Documentation

Base API path: `/api/shift-details`

This API provides endpoints for managing shift details in the system. All endpoints require authorization.

## Endpoints

### Get All Shift Details

**GET** `/api/shift-details/`

Retrieve a list of all shift details based on optional pagination and sorting parameters.

**Query Parameters**
- `limit` (optional): Maximum number of shift details to return
- `skip` (optional): Number of shift details to skip
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Direction to sort (asc/desc)

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "08:00:00",
      "check_in_end_time": "08:30:00",
      "check_out_start_time": "17:00:00",
      "check_out_end_time": "17:30:00",
      "total_working_hours": 8.5,
      "is_overnight": false,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.getShiftDetails`

---

### Get Shift Detail By ID

**GET** `/api/shift-details/:id`

Retrieve a specific shift detail by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the shift detail

**Response**
```json
{
  "shiftDetail": {
    "id": "string",
    "shift_id": "string",
    "check_in_start_time": "08:00:00",
    "check_in_end_time": "08:30:00",
    "check_out_start_time": "17:00:00",
    "check_out_end_time": "17:30:00",
    "total_working_hours": 8.5,
    "is_overnight": false,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

**Error Responses**
- `400 Bad Request`: When shift detail ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `404 Not Found`: When the shift detail with the provided ID doesn't exist
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.getShiftDetailById`

---

### Create Shift Detail

**POST** `/api/shift-details/`

Create a new shift detail.

**Request Body**
```json
{
  "tenant_id": "string",                    // required - The tenant ID
  "shift_start": "2023-01-01T08:00:00Z",   // optional - Start time of the shift
  "clock_in_start": "2023-01-01T07:30:00Z", // optional - Start time for check-in window
  "allow_late": false,                      // required - Allow late arrival
  "late_cutoff": false,                     // required - Cut off half shift for late arrival
  "late_allowance": 15,                     // optional - Minutes allowed for late arrival
  "late_threshold": 30,                     // optional - Minutes threshold for half shift cutoff
  "has_break": false,                       // required - Has break time
  "break_start": "2023-01-01T12:00:00Z",   // optional - Break start time
  "break_end": "2023-01-01T13:00:00Z",     // optional - Break end time
  "shift_end": "2023-01-01T17:00:00Z",     // optional - End time of the shift
  "clock_out_latest": "2023-01-01T18:00:00Z", // optional - Latest time for check-out
  "allow_early_out": false,                 // required - Allow early check-out
  "early_threshold": 15,                    // optional - Minutes threshold for early leave
  "early_no_half_shift": false,             // required - No half shift for early leave
  "early_cutoff": 30,                       // optional - Minutes cutoff for half shift
  "worked_hours": 8,                        // optional - Total working hours
  "clock_in_at_start": false,               // required - Require check-in
  "late_threshold_all_shift": 60            // required - Minutes threshold for no work credit
}
```

**Response**
```json
{
  "shiftDetail": {
    "id": "string",
    "shift_id": "string",
    "check_in_start_time": "08:00:00",
    "check_in_end_time": "08:30:00",
    "check_out_start_time": "17:00:00",
    "check_out_end_time": "17:30:00",
    "total_working_hours": 8.5,
    "is_overnight": false,
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

**Error Responses**
- `400 Bad Request`: When required fields are missing
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.createShiftDetail`

---

### Update Shift Detail

**PUT** `/api/shift-details/:id`

Update an existing shift detail.

**Path Parameters**
- `id` (required): The unique identifier of the shift detail to update

**Request Body**
```json
{
  "tenant_id": "string",                    // optional - The tenant ID
  "shift_start": "2023-01-01T08:00:00Z",   // optional - Start time of the shift
  "clock_in_start": "2023-01-01T07:30:00Z", // optional - Start time for check-in window
  "allow_late": false,                      // optional - Allow late arrival
  "late_cutoff": false,                     // optional - Cut off half shift for late arrival
  "late_allowance": 15,                     // optional - Minutes allowed for late arrival
  "late_threshold": 30,                     // optional - Minutes threshold for half shift cutoff
  "has_break": false,                       // optional - Has break time
  "break_start": "2023-01-01T12:00:00Z",   // optional - Break start time
  "break_end": "2023-01-01T13:00:00Z",     // optional - Break end time
  "shift_end": "2023-01-01T17:00:00Z",     // optional - End time of the shift
  "clock_out_latest": "2023-01-01T18:00:00Z", // optional - Latest time for check-out
  "allow_early_out": false,                 // optional - Allow early check-out
  "early_threshold": 15,                    // optional - Minutes threshold for early leave
  "early_no_half_shift": false,             // optional - No half shift for early leave
  "early_cutoff": 30,                       // optional - Minutes cutoff for half shift
  "worked_hours": 8,                        // optional - Total working hours
  "clock_in_at_start": false,               // optional - Require check-in
  "late_threshold_all_shift": 60            // optional - Minutes threshold for no work credit
}
```

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When shift detail ID is not provided or update fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.updateShiftDetail`

---

### Delete Shift Detail

**DELETE** `/api/shift-details/:id`

Delete a shift detail by its ID.

**Path Parameters**
- `id` (required): The unique identifier of the shift detail to delete

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When shift detail ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Needs policy for `ShiftDetailController.deleteShiftDetail`

---

### Get Shift Details By Shift ID

**GET** `/api/shift-details/shift/:shiftId`

Retrieve all shift details associated with a specific shift.

**Path Parameters**
- `shiftId` (required): The ID of the shift

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "08:00:00",
      "check_in_end_time": "08:30:00",
      "check_out_start_time": "17:00:00",
      "check_out_end_time": "17:30:00",
      "total_working_hours": 8.5,
      "is_overnight": false,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Overnight Shifts

**GET** `/api/shift-details/overnight`

Retrieve all shift details that are configured for overnight shifts.

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "22:00:00",
      "check_in_end_time": "22:30:00",
      "check_out_start_time": "06:00:00",
      "check_out_end_time": "06:30:00",
      "total_working_hours": 8.0,
      "is_overnight": true,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Get Shift Details By Working Hours

**GET** `/api/shift-details/working-hours/:hours`

Retrieve all shift details with a specific number of total working hours.

**Path Parameters**
- `hours` (required): The total working hours to filter by

**Response**
```json
{
  "shiftDetails": [
    {
      "id": "string",
      "shift_id": "string",
      "check_in_start_time": "08:00:00",
      "check_in_end_time": "08:30:00",
      "check_out_start_time": "17:00:00",
      "check_out_end_time": "17:30:00",
      "total_working_hours": 8.5,
      "is_overnight": false,
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

**Error Responses**
- `400 Bad Request`: When hours parameter is not provided or invalid
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user

---

### Delete Shift Details By Shift ID

**DELETE** `/api/shift-details/shift/:shiftId`

Delete all shift details associated with a specific shift.

**Path Parameters**
- `shiftId` (required): The ID of the shift

**Response**
```json
{
  "success": true
}
```

**Error Responses**
- `400 Bad Request`: When shift ID is not provided or deletion fails
- `401 Unauthorized`: When the user is not authenticated
- `500 Internal Server Error`: When there is a server error

**Authorization**
Requires a valid authenticated user