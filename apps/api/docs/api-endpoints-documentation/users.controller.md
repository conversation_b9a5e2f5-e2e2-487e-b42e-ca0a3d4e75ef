# Users Controller API Documentation

Base path: `/api/users`

## Overview

The Users Controller provides endpoints for managing user accounts, including CRUD operations, authentication, and user-specific data retrieval. All endpoints require proper authorization unless otherwise specified.

## Security Notes

- **Tenant Isolation**: The `/api/users/tenant` endpoint automatically extracts tenant ID from the access token, ensuring users can only access data within their organization.
- **Authorization**: All endpoints require a valid JWT access token in the Authorization header.
- **Role-based Access**: Some operations may require specific permissions based on user roles.

## Endpoints

### 1. Get All Users

**Endpoint:** `GET /api/users/`

**Description:** Retrieves a list of all users with optional pagination and sorting.

**Parameters:**
- Query Parameters:
  - `limit` (optional): Number of users to retrieve
  - `skip` (optional): Number of users to skip
  - `sortBy` (optional): Field to sort by
  - `sortDirection` (optional): Sort direction (asc/desc)

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "status": "active|inactive|suspended|pending",
        "code": "string",
        "unit_id": "string",
        "member_role_id": "string",
        "created_at": "timestamp",
        "updated_at": "timestamp"
      }
    ]
  }
}
```

### 2. Get Users By Unit ID

**Endpoint:** `GET /api/users/unit/:unitId`

**Description:** Finds users belonging to a specific unit with pagination. Returns populated user data including unit and tenant information.

**Parameters:**
- Path Parameters:
  - `unitId` (required): ID of the unit
- Query Parameters:
  - `pageSize` (optional): Number of users per page (default: 10)
  - `pageIndex` (optional): Page number (default: 0)

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "phone": "string",
        "status": "active|inactive|suspended|pending",
        "code": "string",
        "unit_id": "string",
        "member_role_id": "string",
        "dob": "date",
        "gender": "male|female|other",
        "avatar_id": "string",
        "face_id": "string",
        "unit": {
          "id": "string",
          "name": "string",
          "organization_id": "string"
        },
        "tenant": {
          "id": "string",
          "name": "string"
        },
        "created_at": "timestamp",
        "updated_at": "timestamp"
      }
    ],
    "pageSize": 10,
    "pageIndex": 0,
    "totalPages": 5,
    "totalCount": 50
  }
}
```

### 3. Get Users By Member Role ID

**Endpoint:** `GET /api/users/role/:memberRoleId`

**Description:** Finds users with a specific member role.

**Parameters:**
- Path Parameters:
  - `memberRoleId` (required): ID of the member role

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "status": "active|inactive|suspended|pending",
        "code": "string",
        "unit_id": "string",
        "member_role_id": "string"
      }
    ]
  }
}
```

### 4. Get Users By Tenant ID (Token-Based)

**Endpoint:** `GET /api/users/tenant`

**Description:** Finds users belonging to the current tenant with pagination. The tenant ID is automatically extracted from the access token, ensuring secure tenant isolation.

**Security Features:**
- Tenant ID is extracted from JWT access token
- Users can only access data within their organization
- Prevents tenant ID manipulation attacks

**Parameters:**
- Query Parameters:
  - `pageSize` (optional): Number of users per page (default: 10)
  - `pageIndex` (optional): Page number (default: 0)

**Authorization:** Required (tenant ID extracted from access token)

**Returns:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "name": "string",
        "email": "string",
        "phone": "string",
        "status": "active|inactive|suspended|pending",
        "code": "string",
        "unit_id": "string",
        "member_role_id": "string",
        "dob": "date",
        "gender": "male|female|other",
        "avatar_id": "string",
        "face_id": "string",
        "unit": {
          "id": "string",
          "name": "string",
          "organization_id": "string"
        },
        "tenant": {
          "id": "string",
          "name": "string"
        },
        "created_at": "timestamp",
        "updated_at": "timestamp"
      }
    ],
    "pageSize": 10,
    "pageIndex": 0,
    "totalPages": 5,
    "totalCount": 50
  }
}
```

### 5. Authenticate User

**Endpoint:** `POST /api/users/authenticate`

**Description:** Authenticates a user using username and password. Returns user information and authentication token.

**Parameters:**
- Body:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "email": "string",
      "status": "active|inactive|suspended|pending",
      "unit_id": "string",
      "tenant_id": "string"
    },
    "token": "string"
  }
}
```

### 6. Get User By ID

**Endpoint:** `GET /api/users/:id`

**Description:** Retrieves a specific user by their ID with complete profile information.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "email": "string",
      "phone": "string",
      "status": "active|inactive|suspended|pending",
      "code": "string",
      "unit_id": "string",
      "member_role_id": "string",
      "dob": "date",
      "gender": "male|female|other",
      "avatar_id": "string",
      "face_id": "string",
      "created_at": "timestamp",
      "updated_at": "timestamp",
      "created_by": "string"
    }
  }
}
```

### 7. Create User

**Endpoint:** `POST /api/users/`

**Description:** Creates a new user account with the specified information.

**Parameters:**
- Body:
  ```json
  {
    "username": "string",
    "password": "string",
    "name": "string",
    "email": "string",
    "phone": "string",
    "unit_id": "string",
    "member_role_id": "string",
    "code": "string", // Optional, auto-generated if not provided
    "dob": "date", // Optional
    "gender": "male|female|other" // Optional
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "name": "string",
      "email": "string",
      "phone": "string",
      "status": "pending",
      "code": "string",
      "unit_id": "string",
      "member_role_id": "string",
      "dob": "date",
      "gender": "male|female|other",
      "created_at": "timestamp",
      "created_by": "string"
    }
  },
  "message": "User created successfully"
}
```

### 8. Update Users Status (Bulk Operation)

**Endpoint:** `PUT /api/users/bulk-update-status`

**Description:** Updates the status of multiple users at once. Useful for batch operations.

**Parameters:**
- Body:
  ```json
  {
    "userIds": ["string", "string"],
    "status": "active|inactive|suspended|pending"
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "updatedCount": 2,
    "updatedUsers": ["user_id_1", "user_id_2"]
  },
  "message": "Successfully updated status for 2 user(s)"
}
```

### 9. Upload User Avatar

**Endpoint:** `POST /api/users/:id/avatar`

**Description:** Uploads an avatar image for a specific user. The image is stored in the file storage system.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user
- Body:
  ```json
  {
    "fileName": "string",
    "fileData": "string" // Base64 encoded image data
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "file_id": "string",
    "avatar_url": "/api/storage/files/{fileId}/stream"
  },
  "message": "Avatar uploaded successfully"
}
```

### 10. Update User

**Endpoint:** `PUT /api/users/:id`

**Description:** Updates a specific user's information. Only provided fields will be updated.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user
- Body: User data to update (partial update supported)
  ```json
  {
    "username": "string",
    "name": "string",
    "email": "string",
    "phone": "string",
    "unit_id": "string",
    "member_role_id": "string",
    "status": "active|inactive|suspended|pending",
    "code": "string",
    "dob": "date",
    "gender": "male|female|other"
    // Note: Password updates should be handled through separate endpoint
  }
  ```

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "User updated successfully"
}
```

### 11. Delete User

**Endpoint:** `DELETE /api/users/:id`

**Description:** Permanently deletes a specific user account. This action cannot be undone.

**Parameters:**
- Path Parameters:
  - `id` (required): ID of the user

**Authorization:** Required

**Returns:**
```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "User deleted successfully"
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "success": false,
  "error": "Validation error message",
  "details": {
    "field": "error description"
  }
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": "Insufficient permissions"
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "User not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Internal server error"
}
```

## Status Values

User status can be one of the following values:
- `active`: User is active and can access the system
- `inactive`: User is temporarily disabled
- `suspended`: User is suspended due to policy violations
- `pending`: User account is pending activation

## Notes

1. **Tenant Isolation**: The `/api/users/tenant` endpoint ensures users can only access data within their organization by extracting tenant ID from the access token.

2. **Pagination**: Endpoints that support pagination return `pageSize`, `pageIndex`, `totalPages`, and `totalCount` for proper pagination handling.

3. **Population**: Some endpoints return populated data (e.g., unit and tenant information) for better frontend integration.

4. **Security**: All endpoints require proper authorization. Sensitive operations may require additional role-based permissions.

5. **File Uploads**: Avatar uploads are handled through the file storage system and return file IDs and streaming URLs.
