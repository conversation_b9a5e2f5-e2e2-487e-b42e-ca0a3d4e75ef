# Tenant Switching với Refresh Token API

## 🎯 Tổng quan

Tính năng tenant switching cho phép người dùng chuyển đổi giữa các tổ chức (tenants) mà không cần đăng nhập lại. <PERSON>hi chuyển đổi tenant, hệ thống sẽ call API refresh-token với `tenantId` mới để nhận access_token có `tenant_id` được cập nhật.

## 🔄 Luồng hoạt động

### 1. **Tenant Switching Flow**
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Database

    User->>Frontend: Chọn tenant mới
    Frontend->>API: POST /api/identity/refresh-token {tenantId}
    API->>Database: Verify refresh token
    API->>Database: Get user info
    API->>API: Create new token with new tenantId
    API->>Frontend: Return new access_token with tenant_id
    Frontend->>Frontend: Update access_token in storage
    Frontend->>Frontend: Invalidate all queries
    Frontend->>User: <PERSON><PERSON><PERSON> thị "Chuyển đổi thành công"
```

### 2. **Create Tenant with Auto-Switch Flow**
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Database

    User->>Frontend: Tạo tổ chức mới
    Frontend->>API: POST /api/tenants {name, address}
    API->>Database: Create new tenant
    API->>Frontend: Return new tenant data
    Frontend->>API: POST /api/identity/refresh-token {tenantId: newTenantId}
    API->>Database: Verify refresh token
    API->>API: Create new token with new tenantId
    API->>Frontend: Return new access_token with tenant_id
    Frontend->>Frontend: Update access_token in storage
    Frontend->>Frontend: Update selected tenant
    Frontend->>Frontend: Invalidate all queries
    Frontend->>User: Hiển thị "Đã tạo và chuyển đổi thành công"
```

## 🛠️ Thay đổi Backend

### 1. **Identity Controller** (`apps/api/src/controllers/identity.controller.ts`)

#### Cập nhật refresh-token endpoint:

```typescript
@HttpPost('/refresh-token')
@AllowAnonymous()
async refreshToken(@HttpContext() context: Context): Promise<Response> {
  // Get tenant switch info from request body
  const body = await context.req.json();
  const switchToTenantId = body.tenantId; // New tenant ID for tenant switching

  // Determine tenant ID - use switchToTenantId if provided
  const tenantId = switchToTenantId || user.unit_id;

  // Log tenant switch if applicable
  if (switchToTenantId && switchToTenantId !== user.unit_id) {
    logger.info('Tenant switch requested during token refresh', {
      userId: user.id,
      fromTenant: user.unit_id,
      toTenant: switchToTenantId,
      ipAddress,
    });
  }

  // Create token pair with the determined tenant ID
  const tokenDoc = await this.identityService.createTokenPair(
    user.id,
    user.username,
    primaryRole,
    tenantId, // Use the determined tenant ID (either switched or original)
    user.member_role_id,
    deviceInfo,
    ipAddress,
    userAgent,
  );

  // Return response with tenant information
  return this.success(context, {
    access_token: tokenDoc.token,
    expires_in: Math.floor((tokenDoc.expires_at.getTime() - Date.now()) / 1000),
    token_type: tokenDoc.token_type,
    tenant_id: tenantId, // Include tenant ID in response
    tenant_switched: switchToTenantId ? true : false, // Indicate if tenant was switched
  });
}
```

### 2. **Identity Service** (`apps/api/src/services/IdentityService.ts`)

#### Cập nhật generateToken và createTokenPair:

```typescript
generateToken(
  userId: string,
  username: string,
  role: string,
  tenantId: string, // Can be different from user's unit_id for tenant switching
  memberRoleId?: string,
  policies?: any[],
  permissions?: string[],
  roles?: string[],
): string {
  const payload: TokenPayload = {
    sub: userId,
    username,
    role,
    unit_id: tenantId, // Use the provided tenant ID
    tenant_id: tenantId, // Add tenant_id for multi-tenancy support
    member_role_id: memberRoleId,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600 * 2, // 2 hours
  };

  return jwt.sign(payload, environment.jwt.secret);
}
```

## 🎨 Thay đổi Frontend

### 1. **use-identity-actions.ts** (`apps/web/src/hooks/use-identity-actions.ts`)

#### Cập nhật refreshAccessToken để hỗ trợ tenant switching:

```typescript
// Refresh access token action with optional tenant switching
const refreshAccessToken = useCallback(async (tenantId?: string) => {
  const globalState = getGlobalRefreshState()

  // Don't refresh on auth pages
  if (globalState.isOnAuthPage) {
    console.debug('Skipping token refresh on auth page')
    return null
  }

  // Check if global refresh is already in progress
  if (globalState.isRefreshing && globalState.refreshPromise) {
    console.debug('Global token refresh already in progress, waiting...')
    return globalState.refreshPromise
  }

  // Prevent multiple simultaneous local refresh attempts
  if (isRefreshingLocal) {
    console.debug('Local token refresh already in progress, skipping')
    return null
  }

  try {
    isRefreshingLocal = true
    console.debug('Starting token refresh...', tenantId ? `with tenant switch to ${tenantId}` : '')

    const deviceInfo = getDeviceInfo()
    const payload = tenantId ? { deviceInfo, tenantId } : { deviceInfo }
    const response = await refreshTokenMutation.mutateAsync(payload)

    // Validate response structure
    if (!response || !response.data?.access_token) {
      console.error('Invalid refresh response:', response)
      throw new Error('Invalid response from server - missing access token')
    }

    // Store new access token
    setAccessToken(response.data?.access_token)

    // Schedule next refresh (with validation)
    if (response.data?.access_token && scheduleTokenRefreshRef.current) {
      scheduleTokenRefreshRef.current(response.data?.access_token)
    }

    // Invalidate queries to refetch with new token
    if (tenantId) {
      // For tenant switching, invalidate all queries
      queryClient.invalidateQueries()
      console.debug('Token refreshed successfully with tenant switch')
    } else {
      // For normal refresh, only invalidate identity queries
      queryClient.invalidateQueries({ queryKey: ['identity'] })
      console.debug('Token refreshed successfully')
    }

    return {
      access_token: response.data?.access_token,
      tenant_switched: !!tenantId,
      tenant_id: response.data?.tenant_id
    }
  } catch (error: any) {
    console.error('Token refresh failed:', error)
    // Handle error...
    throw error
  } finally {
    isRefreshingLocal = false
  }
}, [refreshTokenMutation, navigate, queryClient])
```

### 2. **use-identity-query.ts** (`apps/web/src/hooks/use-identity-query.ts`)

#### Cập nhật useRefreshTokenMutation để hỗ trợ tenantId:

```typescript
export const useRefreshTokenMutation = (
  options?: UseMutationOptions<
    APIResponse<RefreshTokenResponse>,
    Error,
    { deviceInfo?: any; tenantId?: string }
  >,
) => {
  return useMutation<
    APIResponse<RefreshTokenResponse>,
    Error,
    { deviceInfo?: any; tenantId?: string }
  >({
    mutationFn: async (request = {}) => {
      const response = await axiosClient.post(
        '/api/identity/refresh-token',
        request,
        {
          withCredentials: true, // Include HttpOnly cookies
        },
      )
      return response.data
    },
    ...options,
  })
}
```

### 3. **use-tenant-actions.ts** (`apps/web/src/hooks/use-tenant-actions.ts`)

#### Cập nhật handleTenantChange để sử dụng refreshAccessToken:

```typescript
const handleTenantChange = useCallback(async (tenantId: string) => {
  console.log('Tenant selected:', tenantId)

  // Find the full tenant object
  const tenant = getTenantById(tenantId)
  if (!tenant) {
    toast.error('Không tìm thấy tổ chức')
    return
  }

  try {
    // Call refresh-token API with tenant switch
    await withLoading(
      async () => {
        const result = await refreshAccessToken(tenantId)

        if (result?.tenant_switched) {
          // Update persistent storage with full tenant object after successful API call
          setTenant({
            _id: tenant._id,
            id: tenant.id,
            name: tenant.name,
            address: tenant.address,
            created_by: tenant.created_by,
            createdAt: tenant.createdAt || tenant.created_at,
            updatedAt: tenant.updatedAt || tenant.updated_at,
          })

          toast.success('Đã chuyển đổi tổ chức thành công!')
        } else {
          throw new Error('Tenant switch failed')
        }
      },
      {
        message: `Đang chuyển đổi sang ${tenant.name}...`,
        priority: 5, // High priority for tenant switching
      }
    )
  } catch (error) {
    console.error('Tenant switch failed:', error)
    const message = error instanceof Error ? error.message : 'Không thể chuyển đổi tổ chức'
    toast.error(message)
  }
}, [getTenantById, setTenant, withLoading, refreshAccessToken])
```

#### Thêm handleCreateTenantWithAutoSwitch cho tạo tổ chức với auto-switch:

```typescript
// Handle create tenant with auto-switch
const handleCreateTenantWithAutoSwitch = useCallback(
  async (data: CreateTenantRequest) => {
    try {
      await withLoading(
        async () => {
          const result = await createTenant(data)

          if (result.success && result.shouldAutoSwitch && result.tenant) {
            console.log('Auto-switching to newly created tenant:', result.tenant.name)

            // Call refresh token to switch to the new tenant
            const refreshResult = await refreshAccessToken(result.tenant.id)

            if (refreshResult?.tenant_switched) {
              // Update persistent storage with the new tenant
              setTenant({
                _id: result.tenant._id,
                id: result.tenant.id,
                name: result.tenant.name,
                address: result.tenant.address,
                created_by: result.tenant.created_by,
                createdAt: result.tenant.createdAt || result.tenant.created_at,
                updatedAt: result.tenant.updatedAt || result.tenant.updated_at,
              })

              toast.success(`Đã tạo và chuyển đổi sang tổ chức "${result.tenant.name}" thành công!`)
            } else {
              toast.success('Tổ chức đã được tạo thành công')
            }
          } else if (result.success) {
            toast.success('Tổ chức đã được tạo thành công')
          } else {
            throw new Error('Failed to create tenant')
          }
        },
        {
          message: 'Đang tạo tổ chức mới...',
          priority: 5, // High priority for tenant creation
        },
      )
    } catch (error) {
      console.error('Failed to create tenant with auto-switch:', error)
      const message = error instanceof Error ? error.message : 'Không thể tạo tổ chức'
      toast.error(message)
    }
  },
  [createTenant, refreshAccessToken, setTenant, withLoading],
)
```

## 📋 API Request/Response

### Request
```http
POST /api/identity/refresh-token
Content-Type: application/json
Cookie: refresh_token=<signed_refresh_token>

{
  "tenantId": "new_tenant_id_here",
  "deviceInfo": {
    "deviceName": "Chrome Browser",
    "deviceType": "browser"
  }
}
```

### Response
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200,
    "token_type": "Bearer",
    "tenant_id": "new_tenant_id_here",
    "tenant_switched": true
  }
}
```

## 🔐 JWT Token Payload

Sau khi tenant switch, JWT token sẽ chứa:

```json
{
  "sub": "user_id",
  "username": "<EMAIL>",
  "role": "admin",
  "unit_id": "new_tenant_id_here",
  "tenant_id": "new_tenant_id_here",
  "member_role_id": "role_id",
  "permissions": ["read", "write"],
  "roles": ["admin"],
  "iat": **********,
  "exp": **********
}
```

## ✨ Tính năng

### 1. **Loading Provider Integration**
- Sử dụng global loading với priority cao (5)
- Hiển thị message: "Đang chuyển đổi sang {tenant_name}..."
- Smooth UX với debounce và minimum display time

### 2. **Error Handling**
- Toast error nếu tenant không tồn tại
- Toast error nếu API call thất bại
- Proper error logging

### 3. **State Management**
- Update access token trong storage
- Invalidate tất cả queries để refetch với token mới
- Update persistent tenant selection

### 4. **Security**
- Sử dụng refresh token từ HttpOnly cookie
- Validate tenant access permissions
- Log tenant switch activities

## 🧪 Testing

### 1. **Test Tenant Switch**
```typescript
// Trong component
const { handleTenantChange, handleCreateTenantWithAutoSwitch } = useTenantSelection()
const { refreshAccessToken } = useIdentityActions()

// Test switch via handleTenantChange
await handleTenantChange('new_tenant_id')

// Test switch via refreshAccessToken directly
const result = await refreshAccessToken('new_tenant_id')
console.log('Tenant switched:', result?.tenant_switched)

// Test create tenant with auto-switch
await handleCreateTenantWithAutoSwitch({
  name: 'New Organization',
  address: '123 Main St'
})
```

### 2. **Test API Endpoint**
```bash
curl -X POST http://localhost:5000/api/identity/refresh-token \
  -H "Content-Type: application/json" \
  -H "Cookie: refresh_token=<signed_token>" \
  -d '{"tenantId": "new_tenant_id"}'
```

## 🎯 Lợi ích

✅ **Seamless UX**: Không cần đăng nhập lại khi chuyển tenant
✅ **Security**: Sử dụng refresh token thay vì expose credentials
✅ **Consistent State**: Tất cả queries được refetch với token mới
✅ **Loading UX**: Smooth loading experience với global provider
✅ **Error Handling**: Comprehensive error handling và user feedback
✅ **Audit Trail**: Log tất cả tenant switch activities
✅ **Clean Architecture**: Sử dụng existing refreshAccessToken thay vì tạo mutation riêng
✅ **Reusable Logic**: refreshAccessToken có thể được sử dụng cho cả normal refresh và tenant switch
✅ **Centralized Token Management**: Tất cả token logic được quản lý tại một nơi
✅ **Auto-Switch on Create**: Tự động chuyển sang tổ chức mới tạo với UX mượt mà
✅ **Unified Create Flow**: Tạo tổ chức và switch trong một flow duy nhất
✅ **Immediate Context**: User ngay lập tức làm việc trong context của tổ chức mới
