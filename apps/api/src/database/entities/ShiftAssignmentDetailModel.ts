import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import {
  ShiftAssignmentDetailAttributes,
  WorkShiftType,
  EntityType,
  ShiftAssignmentType,
  DateRangeType,
  Status
} from '@c-cam/types';

/**
 * Shift Assignment Detail Document Interface
 * Extends the ShiftAssignmentDetailAttributes (excluding _id) and Document
 */
export interface ShiftAssignmentDetailDocument
  extends Omit<ShiftAssignmentDetailAttributes, '_id'>,
    Document {}

/**
 * Shift Assignment Detail Schema
 * Defines the MongoDB schema for shift assignment details
 */
const ShiftAssignmentDetailSchema = createSchema({
  tenant_id: {
    type: String,
    ref: 'tenant',
    required: true,
  },
  shift_assignment_ids: [{
    type: String,
    ref: 'shift_assignment',
  }],
  code: { type: Number, required: true },
  name: { type: String, required: true },
  work_shift_type: {
    type: String,
    enum: Object.values(WorkShiftType),
    required: true,
  },
  entity_type: {
    type: String,
    enum: Object.values(EntityType),
    required: true,
  },
  assignment_type: {
    type: String,
    enum: Object.values(ShiftAssignmentType),
    required: true,
  },
  dateRange_type: {
    type: String,
    enum: Object.values(DateRangeType),
    required: true,
  },
  start_date: { type: Date, required: true },
  end_date: { type: Date, required: true },
  status: {
    type: String,
    enum: Object.values(Status),
    default: Status.Active,
    required: true,
  },
  createdBy: { type: String, required: true },
  targets: [{ type: String }],
  excludedTargets: [{ type: String }],
});

// Add indexes
ShiftAssignmentDetailSchema.index({ tenant_id: 1});
ShiftAssignmentDetailSchema.index({ tenant_id: 1, name: 1 }, { unique: true });

// Create and export the model
const ShiftAssignmentDetailModel = createModel<ShiftAssignmentDetailDocument>(
  'shift_assignment_detail',
  ShiftAssignmentDetailSchema,
);

export default ShiftAssignmentDetailModel;
