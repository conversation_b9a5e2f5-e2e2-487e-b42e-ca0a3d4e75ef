import { createModel, createSchema } from '@c-cam/core';
import { UnitAttributes } from '@c-cam/types';
import { Document } from 'mongoose';

/**
 * Unit Document Interface
 * Extends the UnitAttributes (excluding id) and Document
 */
export interface UnitDocument extends Omit<UnitAttributes, '_id'>, Document {}

/**
 * Unit Schema
 * Defines the MongoDB schema for organizational units
 */
const UnitSchema = createSchema({
  organization_id: {
    type: String,
    ref: 'tenant',
    required: true
  },
  user_id: {
    type: String,
    ref: 'users',
    required: false
  },
  parent_unit_id: {
    type: String,
    ref: 'unit',
    required: false
  },
  name: { type: String, required: true },
  created_by: { type: String, required: true }
});

// Add indexes
UnitSchema.index({ organization_id: 1, name: 1 }, { unique: true });

// Create and export the model
const UnitModel = createModel<UnitDocument>('unit', UnitSchema);

export default UnitModel;
