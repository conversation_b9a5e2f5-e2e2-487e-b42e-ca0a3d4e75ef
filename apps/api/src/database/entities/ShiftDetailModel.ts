import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import {
  ShiftDetailAttributes
} from '@c-cam/types';

/**
 * Shift Detail Document Interface
 * Extends the ShiftDetailAttributes (excluding _id) and Document
 */
export interface ShiftDetailDocument
  extends Omit<ShiftDetailAttributes, '_id'>,
    Document {}

/**
 * Shift Detail Schema
 * Defines the MongoDB schema for shift details
 */
const ShiftDetailSchema = createSchema({
  tenant_id: {
    type: String,
    ref: 'tenant',
    required: true,
  },
  // Temporary field to handle existing database index
  // TODO: Remove this after database migration
  shift_id: {
    type: String,
    required: false,
    default: function() {
      // Generate a unique ID to avoid duplicate key errors
      return new Date().getTime().toString() + Math.random().toString(36).substr(2, 9);
    }
  },
  shift_start: { type: Date }, // Thời gian bắt đầu ca làm việc
  clock_in_start: { type: Date }, // Thời gian bắt đầu cho phép chấm công
  allow_late: { type: Boolean, required: true, default: false }, // Cho phép đến muộn
  late_cutoff: { type: Boolean, required: true, default: false }, // đến sau quá số phút sẽ không ghi nhận nửa ca đầu
  late_allowance: { type: Number }, // Số phút cho phép đến muộn
  late_threshold: { type: Number }, // Số phút vào sau sẽ không ghi nhận nửa ca đầu
  has_break: { type: Boolean, required: true, default: false }, // Nghỉ giữa giờ
  break_start: { type: Date }, // Thời gian bắt đầu nghỉ giữa giờ
  break_end: { type: Date }, // Thời gian kết thúc nghỉ giữa giờ
  shift_end: { type: Date }, // Thời gian kết thúc ca
  clock_out_latest: { type: Date }, // Thời gian muộn nhất cho phép chấm công ra
  allow_early_out: { type: Boolean, required: true, default: false }, // Cho phép chấm công ra trước
  early_threshold: { type: Number }, // Số phút chấm công ra trước quy định bị tính là về sớm
  early_no_half_shift: { type: Boolean, required: true, default: false }, // Cho phép không ghi nhận nửa công ca sau nếu về sớm
  early_cutoff: { type: Number }, // Số phút cho phép về sớm nếu vượt quá sẽ không được tính công nửa ca sau
  worked_hours: { type: Number }, // Số giờ làm việc
  clock_in_at_start: { type: Boolean, required: true, default: false }, // yêu cầu chấm công
  late_threshold_all_shift: { type: Number, required: true, default: 0 }, // Số phút cho phép đến muộn (quá không ghi nhận công cho toàn bộ ca làm việc)
});

// Add indexes
ShiftDetailSchema.index({ tenant_id: 1 });

// Create and export the model
const ShiftDetailModel = createModel<ShiftDetailDocument>(
  'shift_detail',
  ShiftDetailSchema,
);

export default ShiftDetailModel;
