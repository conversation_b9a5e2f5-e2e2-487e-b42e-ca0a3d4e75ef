import {
  Authorized,
  Body,
  Controller,
  ControllerBase,
  HttpContext,
  HttpDelete,
  HttpGet,
  HttpPost,
  HttpPut,
  Inject,
  Param,
  Query,
} from '@c-cam/core';
import { Context } from 'hono';
import UsersService from '../services/UsersService';
import {
  createAuthorizedOptions,
  getClassPolicy,
  methodRef,
} from '../utils/controller-policies.js';

@Controller('/api/users')
@Authorized({ policies: [getClassPolicy(UsersController.name)] })
export class UsersController extends ControllerBase {
  constructor(@Inject(UsersService) private usersService: UsersService) {
    super();
  }

  /**
   * Get all users (filtered by current tenant from access token)
   */
  @HttpGet('/')
  @Authorized(createAuthorizedOptions(UsersController.name, methodRef<UsersController>('getUsers')))
  async getUsers(@HttpContext() c: Context): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');

    // Get tenant ID from access token
    const tenantId = user.tenant_id || user.unit_id;
    this.validateIf(
      !tenantId,
      'Không nhận diện được đơn vị của bạn. Vui lòng liên hệ với quản trị viên.',
    );

    // Get users filtered by tenant
    const users = await this.usersService.findByTenantId(tenantId);

    return this.success(c, { users });
  }

  /**
   * Find users by unit ID with pagination (validates unit belongs to current tenant)
   */
  @HttpGet('/unit/:unitId')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('getUsersByUnitId')),
  )
  async getUsersByUnitId(
    @HttpContext() c: Context,
    @Param('unitId') unitId: string,
    @Query('pageSize') pageSize?: string,
    @Query('pageIndex') pageIndex?: string,
  ): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');
    this.validateIf(!unitId, 'Unit ID is required');

    // Get tenant ID from access token
    const tenantId = user.tenant_id || user.unit_id;
    this.validateIf(
      !tenantId,
      'Không nhận diện được đơn vị của bạn. Vui lòng liên hệ với quản trị viên.',
    );

    // Validate that the unit belongs to the current tenant
    const isValidUnit = await this.usersService.validateUnitBelongsToTenant(unitId, tenantId);
    this.validateIf(!isValidUnit, 'Unit không thuộc về tổ chức của bạn');

    const pageSizeNum = pageSize ? parseInt(pageSize) : 10;
    const pageIndexNum = pageIndex ? parseInt(pageIndex) : 0;

    const result = await this.usersService.findByUnitIdWithPopulatePaginated(
      unitId,
      pageSizeNum,
      pageIndexNum,
    );

    return this.success(c, result);
  }

  /**
   * Find users by member role ID (filtered by current tenant)
   */
  @HttpGet('/role/:memberRoleId')
  @Authorized(
    createAuthorizedOptions(
      UsersController.name,
      methodRef<UsersController>('getUsersByMemberRoleId'),
    ),
  )
  async getUsersByMemberRoleId(
    @HttpContext() c: Context,
    @Param('memberRoleId') memberRoleId: string,
  ): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');
    this.validateIf(!memberRoleId, 'Member role ID is required');

    // Get tenant ID from access token
    const tenantId = user.tenant_id || user.unit_id;
    this.validateIf(
      !tenantId,
      'Không nhận diện được đơn vị của bạn. Vui lòng liên hệ với quản trị viên.',
    );

    const users = await this.usersService.findByMemberRoleIdAndTenant(memberRoleId, tenantId);
    return this.success(c, { users });
  }

  /**
   * Authenticate a user
   */
  @HttpPost('/authenticate')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('authenticate')),
  )
  async authenticate(
    @HttpContext() c: Context,
    @Body() credentials: { username: string; password: string },
  ): Promise<Response> {
    // Validate required fields
    this.validateRequiredFields(credentials, ['username', 'password']);

    const user = await this.usersService.authenticate(credentials.username, credentials.password);

    this.unauthorizedIf(!user, 'Invalid credentials');

    return this.success(c, { user });
  }

  /**
   * Get a user by ID
   */
  @HttpGet('/:id')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('getUserById')),
  )
  async getUserById(@HttpContext() c: Context, @Param('id') id: string): Promise<Response> {
    await this.getCurrentUser(c.req);
    this.validateIf(!id, 'User ID is required');

    const user = await this.usersService.findById(id);
    this.notFoundIf(!user, 'User not found');

    return this.success(c, { user });
  }

  /**
   * Create a new user
   */
  @HttpPost('/')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('createUser')),
  )
  async createUser(@HttpContext() c: Context, @Body() userData: any): Promise<Response> {
    const currentUser = await this.getCurrentUser(c.req);
    const userId = currentUser?.id;

    // Validate required fields
    this.validateRequiredFields(userData, ['username', 'password', 'name', 'unit_id']);

    // Sanitize the data
    const sanitizedData = this.sanitizeData(userData);

    // Add the creator ID
    sanitizedData.created_by = userId;

    // Auto-generate member code if not provided
    if (!sanitizedData.code || sanitizedData.code.trim() === '') {
      sanitizedData.code = await this.usersService.generateMemberCode();
    }

    const user = await this.usersService.createUser(sanitizedData);
    return this.created(c, { user }, 'User created successfully');
  }

  /**
   * Update status for multiple users
   */
  @HttpPut('/bulk-update-status')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('updateUsersStatus')),
  )
  async updateUsersStatus(@HttpContext() c: Context): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    const currentUser = await this.getCurrentUser(c.req);

    const requestData = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(requestData, ['userIds', 'status']);

    const { userIds, status } = requestData;

    // Validate userIds is an array
    this.validateIf(!Array.isArray(userIds), 'userIds must be an array');
    this.validateIf(userIds.length === 0, 'userIds cannot be empty');

    // Validate status
    this.validateIf(!status || typeof status !== 'string', 'status must be a valid string');
    this.validateIf(
      !['active', 'inactive', 'suspended', 'pending'].includes(status),
      'status must be one of: active, inactive, suspended, pending',
    );

    const result = await this.usersService.updateUsersStatus(
      userIds,
      status,
      currentUser?.id || currentUser?.sub,
    );

    return this.success(
      c,
      result,
      `Successfully updated status for ${result.updatedCount} user(s)`,
    );
  }

  /**
   * Upload user avatar
   */
  @HttpPost('/:id/avatar')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('uploadAvatar')),
  )
  async uploadAvatar(
    @HttpContext() c: Context,
    @Param('id') id: string,
    @Body() uploadData: { fileName: string; fileData: string },
  ): Promise<Response> {
    await this.getCurrentUser(c.req);
    this.validateIf(!id, 'User ID is required');
    this.validateRequiredFields(uploadData, ['fileName', 'fileData']);

    const fileId = await this.usersService.uploadUserAvatar(
      id,
      uploadData.fileName,
      uploadData.fileData,
    );
    return this.success(
      c,
      {
        file_id: fileId,
        avatar_url: `/api/storage/files/${fileId}/stream`,
      },
      'Avatar uploaded successfully',
    );
  }

  /**
   * Update a user
   */
  @HttpPut('/:id')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('updateUser')),
  )
  async updateUser(
    @HttpContext() c: Context,
    @Param('id') id: string,
    @Body() userData: any,
  ): Promise<Response> {
    await this.getCurrentUser(c.req);
    this.validateIf(!id, 'User ID is required');

    // Sanitize the data
    const sanitizedData = this.sanitizeData(userData);

    const success = await this.usersService.updateUser(id, sanitizedData);
    this.validateIf(!success, 'Failed to update user');

    return this.success(c, { success: true }, 'User updated successfully');
  }

  /**
   * Delete a user
   */
  @HttpDelete('/:id')
  @Authorized(
    createAuthorizedOptions(UsersController.name, methodRef<UsersController>('deleteUser')),
  )
  async deleteUser(@HttpContext() c: Context, @Param('id') id: string): Promise<Response> {
    await this.getCurrentUser(c.req);
    this.validateIf(!id, 'User ID is required');

    const success = await this.usersService.delete(id);
    this.validateIf(!success, 'Failed to delete user');

    return this.success(c, { success: true }, 'User deleted successfully');
  }
}
