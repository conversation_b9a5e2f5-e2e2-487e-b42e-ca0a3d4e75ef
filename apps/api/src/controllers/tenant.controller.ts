import {
  Authorized,
  Controller,
  ControllerBase,
  HttpContext,
  <PERSON>ttpDelete,
  HttpGet,
  HttpPost,
  HttpPut,
  Inject,
} from '@c-cam/core';
import { Context } from 'hono';
import TenantService from '../services/TenantService';
import {
  createAuthorizedOptions,
  getClassPolicy,
  methodRef,
} from '../utils/controller-policies.js';

@Controller('/api/tenants')
@Authorized({ policies: [getClassPolicy(TenantController.name)] })
export class TenantController extends ControllerBase {
  constructor(@Inject(TenantService) private tenantService: TenantService) {
    super();
  }

  /**
   * Get organizations accessible to current user
   * For security, only returns organizations the user has access to
   */
  @HttpGet('/')
  @Authorized(
    createAuthorizedOptions(TenantController.name, methodRef<TenantController>('getTenants')),
  )
  async getTenants(@HttpContext() c: Context): Promise<Response> {
    try {
      const user = await this.getCurrentUser(c.req);
      if (!user) {
        return this.unauthorized(c, 'Unauthorized');
      }

      // Get tenant ID from access token
      const tenantId = user.tenant_id || user.unit_id;

      if (!tenantId) {
        // If no tenant in token, return empty list for security
        return this.success(c, { organizations: [] });
      }

      // Only return the current user's tenant
      const organization = await this.tenantService.findById(tenantId);
      const organizations = organization ? [organization] : [];

      return this.success(c, { organizations });
    } catch (error: any) {
      return this.error(c, error.message, 500, 'INTERNAL_SERVER_ERROR');
    }
  }

  /**
   * Get an organization by ID (validates user has access to this tenant)
   */
  @HttpGet('/:id')
  @Authorized(
    createAuthorizedOptions(TenantController.name, methodRef<TenantController>('getTenantById')),
  )
  async getTenantById(@HttpContext() c: Context): Promise<Response> {
    try {
      const user = await this.getCurrentUser(c.req);
      if (!user) {
        return this.unauthorized(c, 'Unauthorized');
      }

      const { id } = c.req.param();
      if (!id) {
        return this.badRequest(c, 'Organization ID is required');
      }

      // Get tenant ID from access token
      const userTenantId = user.tenant_id || user.unit_id;

      // Validate user can only access their own tenant
      if (id !== userTenantId) {
        return this.error(c, 'Bạn không có quyền truy cập tổ chức này', 403, 'FORBIDDEN');
      }

      const organization = await this.tenantService.findById(id);

      if (!organization) {
        return this.notFound(c, 'Organization not found');
      }

      return this.success(c, { organization });
    } catch (error: any) {
      return this.error(c, error.message, 500, 'INTERNAL_SERVER_ERROR');
    }
  }

  /**
   * Create a new organization
   */
  @HttpPost('/')
  @Authorized(
    createAuthorizedOptions(TenantController.name, methodRef<TenantController>('createTenant')),
  )
  async createTenant(@HttpContext() c: Context): Promise<Response> {
    try {
      const user = await this.getCurrentUser(c.req);
      if (!user) {
        return this.unauthorized(c, 'Unauthorized');
      }

      const { name } = await c.req.json();

      if (!name) {
        return this.badRequest(c, 'Organization name is required');
      }

      const organization = await this.tenantService.createOrganization(name, user.id);
      return this.created(c, { organization });
    } catch (error: any) {
      return this.error(c, error.message, 500, 'INTERNAL_SERVER_ERROR');
    }
  }

  /**
   * Update an organization
   */
  @HttpPut('/:id')
  @Authorized(
    createAuthorizedOptions(TenantController.name, methodRef<TenantController>('updateTenant')),
  )
  async updateTenant(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = this.getCurrentUser(c.req);
      if (!userId) {
        return this.unauthorized(c, 'Unauthorized');
      }

      const { id } = c.req.param();
      if (!id) {
        return this.badRequest(c, 'Organization ID is required');
      }
      const { name } = await c.req.json();

      if (!name) {
        return this.badRequest(c, 'Organization name is required');
      }

      const success = await this.tenantService.updateOrganization(id, name);

      if (!success) {
        return this.badRequest(c, 'Failed to update organization');
      }

      return this.success(c, { success: true });
    } catch (error: any) {
      return this.error(c, error.message, 500, 'INTERNAL_SERVER_ERROR');
    }
  }

  /**
   * Delete an organization
   */
  @HttpDelete('/:id')
  @Authorized(
    createAuthorizedOptions(TenantController.name, methodRef<TenantController>('deleteTenant')),
  )
  async deleteTenant(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Organization ID is required' }, 400);
      }
      const success = await this.tenantService.delete(id);

      if (!success) {
        return c.json({ error: 'Failed to delete organization' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find organizations by name
   */
  @HttpGet('/name/:name')
  async getTenantByName(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { name } = c.req.param();
      if (!name) {
        return c.json({ error: 'Organization name is required' }, 400);
      }
      const organization = await this.tenantService.findByName(name);

      if (!organization) {
        return c.json({ error: 'Organization not found' }, 404);
      }

      return c.json({ organization });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find organizations created by a specific user
   */
  @HttpGet('/created-by/:createdBy')
  async getTenantsByCreator(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { createdBy } = c.req.param();
      if (!createdBy) {
        return c.json({ error: 'Created by is required' }, 400);
      }
      const organizations = await this.tenantService.findByCreatedBy(createdBy);

      return c.json({ organizations });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }
}
