import {
  Authorized,
  Controller,
  ControllerBase,
  HttpContext,
  HttpDelete,
  HttpGet,
  HttpPost,
  HttpPut,
  Inject,
  Param,
} from '@c-cam/core';
import { Context } from 'hono';
import UnitService from '../services/UnitService';
import {
  createAuthorizedOptions,
  getClassPolicy,
  methodRef,
} from '../utils/controller-policies.js';

@Controller('/api/units')
@Authorized({ policies: [getClassPolicy(UnitController.name)] })
export class UnitController extends ControllerBase {
  constructor(@Inject(UnitService) private unitService: UnitService) {
    super();
  }

  /**
   * Get all units (filtered by current tenant from access token)
   */
  @HttpGet('/')
  @Authorized(createAuthorizedOptions(UnitController.name, methodRef<UnitController>('getUnits')))
  async getUnits(@HttpContext() c: Context): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');

    // Get tenant ID from access token
    const tenantId = user.tenant_id || user.unit_id;
    this.validateIf(
      !tenantId,
      'Không nhận diện được đơn vị của bạn. Vui lòng liên hệ với quản trị viên.',
    );

    // Get units filtered by tenant
    const units = await this.unitService.findByTenantId(tenantId);

    return this.success(c, { units });
  }

  /**
   * Get a unit by ID
   */
  @HttpGet('/:id')
  @Authorized(
    createAuthorizedOptions(UnitController.name, methodRef<UnitController>('getUnitById')),
  )
  async getUnitById(@HttpContext() c: Context, @Param('id') id: string): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    await this.getCurrentUser(c.req);
    this.validateIf(!id, 'Unit ID is required');

    const unit = await this.unitService.findById(id);
    this.notFoundIf(!unit, 'Unit not found');

    return this.success(c, { unit });
  }

  /**
   * Create a new organizational unit
   */
  @HttpPost('/')
  @Authorized(createAuthorizedOptions(UnitController.name, methodRef<UnitController>('createUnit')))
  async createUnit(@HttpContext() c: Context): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    const currentUser = await this.getCurrentUser(c.req);

    const unitData = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(unitData, ['name', 'organization_id']);

    // Sanitize the data
    const sanitizedData = this.sanitizeData(unitData);

    // Add the creator ID
    sanitizedData.created_by = currentUser?.id || currentUser?.sub;

    const unit = await this.unitService.createUnit(sanitizedData);
    return this.created(c, { unit }, 'Unit created successfully');
  }

  /**
   * Update an organizational unit
   */
  @HttpPut('/:id')
  @Authorized(createAuthorizedOptions(UnitController.name, methodRef<UnitController>('updateUnit')))
  async updateUnit(@HttpContext() c: Context, @Param('id') id: string): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    await this.getCurrentUser(c.req);
    this.validateIf(!id, 'Unit ID is required');

    const unitData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(unitData);

    const success = await this.unitService.updateUnit(id, sanitizedData);
    this.validateIf(!success, 'Failed to update unit');

    return this.success(c, { success: true }, 'Unit updated successfully');
  }

  /**
   * Delete an organizational unit
   */
  @HttpDelete('/:id')
  @Authorized(createAuthorizedOptions(UnitController.name, methodRef<UnitController>('deleteUnit')))
  async deleteUnit(@HttpContext() c: Context, @Param('id') id: string): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    await this.getCurrentUser(c.req);
    this.validateIf(!id, 'Unit ID is required');

    const success = await this.unitService.delete(id);
    this.validateIf(!success, 'Failed to delete unit');

    return this.success(c, { success: true }, 'Unit deleted successfully');
  }

  /**
   * Get organizational hierarchy for an organization (validates user has access)
   */
  @HttpGet('/organization/:organizationId/hierarchy')
  async getOrganizationalHierarchy(
    @HttpContext() c: Context,
    @Param('organizationId') organizationId: string,
  ): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');
    this.validateIf(!organizationId, 'Organization ID is required');

    // Get tenant ID from access token
    const userTenantId = user.tenant_id || user.unit_id;

    // Validate user can only access their own organization
    if (organizationId !== userTenantId) {
      this.validateIf(true, 'Bạn không có quyền truy cập tổ chức này');
    }

    const hierarchy = await this.unitService.getOrganizationalHierarchy(organizationId);
    return this.success(c, { hierarchy });
  }

  /**
   * Find units by organization ID (validates user has access to this organization)
   */
  @HttpGet('/organization/:organizationId')
  async getUnitsByOrganizationId(
    @HttpContext() c: Context,
    @Param('organizationId') organizationId: string,
  ): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');
    this.validateIf(!organizationId, 'Organization ID is required');

    // Get tenant ID from access token
    const userTenantId = user.tenant_id || user.unit_id;

    // Validate user can only access their own organization
    if (organizationId !== userTenantId) {
      this.validateIf(true, 'Bạn không có quyền truy cập tổ chức này');
    }

    const units = await this.unitService.findByOrganizationId(organizationId);
    return this.success(c, { units });
  }

  /**
   * Find units by user ID
   */
  @HttpGet('/user/:userId')
  async getUnitsByUserId(
    @HttpContext() c: Context,
    @Param('userId') userId: string,
  ): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    await this.getCurrentUser(c.req);
    this.validateIf(!userId, 'User ID is required');

    const units = await this.unitService.findByUserId(userId);
    return this.success(c, { units });
  }

  /**
   * Find units by parent unit ID
   */
  @HttpGet('/parent/:parentUnitId')
  async getUnitsByParentUnitId(
    @HttpContext() c: Context,
    @Param('parentUnitId') parentUnitId: string,
  ): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    await this.getCurrentUser(c.req);
    this.validateIf(!parentUnitId, 'Parent unit ID is required');

    const units = await this.unitService.findByParentUnitId(parentUnitId);
    return this.success(c, { units });
  }

  /**
   * Get root units (units without a parent, filtered by current tenant)
   */
  @HttpGet('/roots')
  async getRootUnits(@HttpContext() c: Context): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');

    // Get tenant ID from access token
    const tenantId = user.tenant_id || user.unit_id;
    this.validateIf(
      !tenantId,
      'Không nhận diện được đơn vị của bạn. Vui lòng liên hệ với quản trị viên.',
    );

    const units = await this.unitService.findRootUnitsByTenant(tenantId);
    return this.success(c, { units });
  }

  /**
   * Find units by tenant (gets tenant ID from access token)
   */
  @HttpGet('/tenant')
  async getUnitsByTenant(@HttpContext() c: Context): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    this.validateIf(!user, 'User authentication required');

    // Get tenant ID from access token
    const tenantId = user.tenant_id || user.unit_id;
    this.validateIf(
      !tenantId,
      'Không nhận diện được đơn vị của bạn. Vui lòng liên hệ với quản trị viên.',
    );

    const units = await this.unitService.findByTenantId(tenantId);
    return this.success(c, { units });
  }

  /**
   * Update unit for multiple users
   */
  @HttpPut('/users/bulk-update')
  @Authorized(
    createAuthorizedOptions(UnitController.name, methodRef<UnitController>('updateUsersUnit')),
  )
  async updateUsersUnit(@HttpContext() c: Context): Promise<Response> {
    // Authentication is handled by @Authorized decorator
    const currentUser = await this.getCurrentUser(c.req);

    const requestData = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(requestData, ['userIds', 'unitId']);

    const { userIds, unitId } = requestData;

    // Validate userIds is an array
    this.validateIf(!Array.isArray(userIds), 'userIds must be an array');
    this.validateIf(userIds.length === 0, 'userIds cannot be empty');

    // Validate unitId
    this.validateIf(!unitId || typeof unitId !== 'string', 'unitId must be a valid string');

    const result = await this.unitService.updateUsersUnit(
      userIds,
      unitId,
      currentUser?.id || currentUser?.sub,
    );

    return this.success(c, result, `Successfully updated unit for ${result.updatedCount} user(s)`);
  }
}
