import { environment } from '@/configs/environment';
import IdentityService from '@/services/IdentityService';
import {
  AllowAnonymous,
  Authorized,
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  Inject,
  NotFoundError,
  UnauthorizedError
} from '@c-cam/core';
import { logger } from '@c-cam/logger';
import { Context } from 'hono';
import { deleteCookie, getSignedCookie, setSignedCookie } from 'hono/cookie';
import {
  createAuthorizedOptions,
  getClassPolicy,
  methodRef,
} from '../utils/controller-policies.js';

/**
 * Controller for handling identity-related operations
 * Includes authentication, token management, and user profile operations
 */
@Controller('/api/identity')
@Authorized({ policies: [getClassPolicy(IdentityController.name)], required: false })
export class IdentityController extends ControllerBase {
  private readonly cookieSecret: string;

  constructor(@Inject(IdentityService) private identityService: IdentityService) {
    super();
    this.cookieSecret = environment.jwt.secret; // Use JWT secret for cookie signing
  }

  /**
   * Override getJwtSecret to provide the JWT secret from environment
   */
  protected getJwtSecret(): string | null {
    return environment.jwt.secret;
  }

  /**
   * Override verifyAndDecodeToken to use IdentityService
   */
  protected async verifyAndDecodeToken(token: string): Promise<any | null> {
    return this.identityService.verifyToken(token);
  }

  /**
   * Override validateUserFromToken to use IdentityService
   */
  protected async validateUserFromToken(payload: any): Promise<any | null> {
    try {
      // Get user from database
      const user = await this.identityService.getUserById(payload.sub);
      if (!user) {
        logger.debug('User not found in database', { userId: payload.sub });
        return null;
      }

      return user;
    } catch (error) {
      logger.error('User validation failed', { error, userId: payload.sub });
      return null;
    }
  }

  /**
   * Login endpoint - authenticate user and return tokens
   */
  @HttpPost('/login')
  @AllowAnonymous()
  async login(@HttpContext() context: Context): Promise<Response> {
    try {
      const { username, password, deviceInfo } = await context.req.json();

      // Validate required fields using ControllerBase method
      this.validateRequiredFields({ username, password }, ['username', 'password']);

      // Authenticate user
      const user = await this.identityService.authenticate(username, password);

      if (!user) {
        logger.warn('Failed login attempt', { username });
        throw new UnauthorizedError('Invalid credentials');
      }

      // Get client information
      const ipAddress =
        context.req.header('x-forwarded-for') || context.req.header('x-real-ip') || 'unknown';

      const userAgent = context.req.header('user-agent') || 'unknown';

      // Get user roles
      const userRoles = await this.identityService.getUserRoles(user.id);
      const primaryRole = userRoles.length > 0 ? userRoles[0] : 'user';
      if (!primaryRole) {
        throw new UnauthorizedError('User has no primary role');
      }

      // Create token pair
      const tokenDoc = await this.identityService.createTokenPair(
        user.id,
        user.username,
        primaryRole,
        user.unit_id,
        user.member_role_id,
        deviceInfo,
        ipAddress,
        userAgent,
      );

      // Set refresh token as signed HttpOnly cookie with expiry matching token expiry
      const refreshExpirySeconds = Math.floor(
        (tokenDoc.refresh_expires_at.getTime() - Date.now()) / 1000,
      );

      await setSignedCookie(context, 'refresh_token', tokenDoc.refresh_token, this.cookieSecret, {
        httpOnly: true,
        secure: true,
        sameSite: 'Strict',
        path: '/api/identity',
        maxAge: refreshExpirySeconds, // Use actual token expiry
      });

      logger.info('User logged in successfully', {
        userId: user.id,
        username: user.username,
        ipAddress,
        sessionId: tokenDoc.session_id,
      });

      // Return access token in response body, refresh token in signed HttpOnly cookie
      return this.success(context, {
        access_token: tokenDoc.token,
        expires_in: Math.floor((tokenDoc.expires_at.getTime() - Date.now()) / 1000),
        token_type: tokenDoc.token_type,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          email: user.email,
          phone: user.phone,
          dob: user.dob,
          gender: user.gender,
          unit_id: user.unit_id,
          member_role_id: user.member_role_id,
          face_id: user.face_id,
          roles: userRoles,
          created_at: user.created_at,
        },
      });
    } catch (error: any) {
      logger.error('Login error', { error: error.message });
      throw error;
    }
  }

  /**
   * Refresh token endpoint - get new access token using refresh token from signed HttpOnly cookie
   * Supports tenant switching by providing tenantId in request body
   */
  @HttpPost('/refresh-token')
  @AllowAnonymous()
  async refreshToken(@HttpContext() context: Context): Promise<Response> {
    try {
      // Get refresh token from signed HttpOnly cookie
      const refreshToken = await getSignedCookie(context, this.cookieSecret, 'refresh_token');

      if (!refreshToken) {
        throw new UnauthorizedError('Refresh token is required. Please login again.');
      }

      // Get client information
      const ipAddress =
        context.req.header('x-forwarded-for') || context.req.header('x-real-ip') || 'unknown';

      const userAgent = context.req.header('user-agent') || 'unknown';

      // Get device info and tenant switch info from request body (optional)
      const body = await context.req.json();
      const deviceInfo = body.deviceInfo;
      const switchToTenantId = body.tenantId; // New tenant ID for tenant switching

      // Check refresh token validity - using refreshToken method which returns a session or null
      const session = await this.identityService.refreshToken(
        refreshToken,
        undefined,
        ipAddress,
        userAgent,
      );

      if (!session) {
        // Invalid or expired refresh token
        // Clear the invalid signed cookie
        deleteCookie(context, 'refresh_token', {
          path: '/api/identity',
        });

        throw new UnauthorizedError('Invalid refresh token');
      }

      // Get user information
      const user = await this.identityService.getUserById(session.user_id);

      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Get user roles
      const userRoles = await this.identityService.getUserRoles(user.id);
      const primaryRole = userRoles.length > 0 ? userRoles[0] : 'user';
      if (!primaryRole) {
        throw new UnauthorizedError('User has no primary role');
      }

      // Determine tenant ID - use switchToTenantId if provided for tenant switching, otherwise use user's unit_id
      const tenantId = switchToTenantId || user.unit_id;

      // Log tenant switch if applicable
      if (switchToTenantId && switchToTenantId !== user.unit_id) {
        logger.info('Tenant switch requested during token refresh', {
          userId: user.id,
          fromTenant: user.unit_id,
          toTenant: switchToTenantId,
          ipAddress,
        });
      }

      // Create token pair with the determined tenant ID
      const tokenDoc = await this.identityService.createTokenPair(
        user.id,
        user.username,
        primaryRole,
        tenantId, // Use the determined tenant ID (either switched or original)
        user.member_role_id,
        deviceInfo,
        ipAddress,
        userAgent,
      );

      // Set new refresh token as signed HttpOnly cookie with expiry matching token expiry
      const refreshExpirySeconds = Math.floor(
        (tokenDoc.refresh_expires_at.getTime() - Date.now()) / 1000,
      );

      await setSignedCookie(context, 'refresh_token', tokenDoc.refresh_token, this.cookieSecret, {
        httpOnly: true,
        secure: true,
        sameSite: 'Strict',
        path: '/api/identity',
        maxAge: refreshExpirySeconds, // Use actual token expiry
      });

      logger.info('Token refreshed successfully', {
        userId: user.id,
        ipAddress,
        sessionId: tokenDoc.session_id,
        rotationCount: tokenDoc.rotation_count,
        tenantId: tenantId,
        tenantSwitched: switchToTenantId ? true : false,
      });

      // Return the new access token with tenant information
      return this.success(context, {
        access_token: tokenDoc.token,
        expires_in: Math.floor((tokenDoc.expires_at.getTime() - Date.now()) / 1000),
        token_type: tokenDoc.token_type,
        tenant_id: tenantId, // Include tenant ID in response
        tenant_switched: switchToTenantId ? true : false, // Indicate if tenant was switched
      });
    } catch (error: any) {
      logger.error('Refresh token error', { error: error.message });
      throw error;
    }
  }

  /**
   * Logout endpoint - invalidate the current session
   */
  @HttpPost('/logout')
  @Authorized(
    createAuthorizedOptions(IdentityController.name, methodRef<IdentityController>('logout')),
  )
  async logout(@HttpContext() context: Context): Promise<Response> {
    try {
      // Get access token from Authorization header
      const authHeader = context.req.header('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedError('Authorization header is required');
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Get refresh token from signed HttpOnly cookie
      const refreshToken = await getSignedCookie(context, this.cookieSecret, 'refresh_token');

      // Clear refresh token cookie
      deleteCookie(context, 'refresh_token', {
        path: '/api/identity',
      });

      // Invalidate the current session - using separate methods to revoke both tokens
      const accessTokenRevoked = await this.identityService.revokeToken(token);

      // Revoke the refresh token if present
      if (refreshToken) {
        await this.identityService.revokeRefreshToken(refreshToken);
      }

      logger.info('User logged out', { success: accessTokenRevoked });

      // Return success response (cookie already cleared above)
      return this.success(context, { success: true });
    } catch (error: any) {
      logger.error('Logout error', { error: error.message });
      throw error;
    }
  }

  /**
   * Logout from all devices - invalidate all sessions for the user
   */
  @HttpPost('/logout-all')
  @Authorized(
    createAuthorizedOptions(IdentityController.name, methodRef<IdentityController>('logoutAll')),
  )
  async logoutAll(@HttpContext() context: Context): Promise<Response> {
    const userId = await this.getCurrentUser(context);

    // Revoke all tokens for the user
    const revokedCount = await this.identityService.revokeAllUserTokens(userId);

    logger.info('User logged out from all devices', {
      userId,
      sessionsTerminated: revokedCount,
    });

    // Clear refresh token cookie
    deleteCookie(context, 'refresh_token', {
      path: '/api/identity',
    });

    // Return success response
    return this.success(context, {
      success: true,
      count: revokedCount,
    });
  }

  /**
   * Get current user profile and permissions
   */
  @HttpGet('/me')
  @Authorized(
    createAuthorizedOptions(IdentityController.name, methodRef<IdentityController>('getMe')),
  )
  async getMe(@HttpContext() context: Context): Promise<Response> {
    const currentUser = await this.getCurrentUser(context.req);
    const userId = currentUser?.id;

    // Get user with roles
    const userWithRoles = await this.identityService.getUserWithRoles(userId);
    this.notFoundIf(!userWithRoles, 'User not found');

    // Get full user details
    const user = await this.identityService.getUserById(userId);
    this.notFoundIf(!user, 'User not found');

    // Get user permissions
    const permissions = await this.identityService.getUserPermissions(userId);

    return this.success(context, {
      user: {
        id: user!.id,
        username: user!.username,
        name: user!.name,
        email: user!.email,
        phone: user!.phone,
        dob: user!.dob,
        gender: user!.gender,
        unit_id: user!.unit_id,
        member_role_id: user!.member_role_id,
        face_id: user!.face_id,
        roles: userWithRoles!.roles,
        permissions: permissions,
        created_at: user!.created_at,
      },
    });
  }

  /**
   * Update current user profile
   */
  @HttpPut('/me')
  @Authorized(
    createAuthorizedOptions(
      IdentityController.name,
      methodRef<IdentityController>('updateProfile'),
    ),
  )
  async updateProfile(@HttpContext() context: Context): Promise<Response> {
    const userId = await this.getCurrentUser(context);
    const profileData = await context.req.json();

    // Validate allowed fields for profile update
    const allowedFields = ['name', 'email', 'phone', 'dob', 'gender'];
    const updateData: any = {};

    for (const field of allowedFields) {
      if (profileData[field] !== undefined) {
        updateData[field] = profileData[field];
      }
    }

    this.validateIf(Object.keys(updateData).length === 0, 'No valid fields provided for update');

    // Update user profile
    const updated = await this.identityService.updateUserProfile(userId, updateData);

    this.validateIf(!updated, 'Failed to update profile');

    logger.info('User profile updated', {
      userId,
      fields: Object.keys(updateData),
    });

    return this.success(context, { success: true }, 'Profile updated successfully');
  }

  /**
   * Get active sessions for the current user
   */
  @HttpGet('/sessions')
  async getSessions(@HttpContext() context: Context): Promise<Response> {
    const userId = await this.getCurrentUser(context);

    try {
      // Get active sessions using the identity service
      const sessions = await this.identityService.getActiveSessions(userId);

      // Transform session data for client
      const formattedSessions = sessions.map((session) => ({
        id: session.id,
        device_info: session.device_info || { deviceName: 'Unknown', deviceType: 'unknown' },
        ip_address: session.ip_address || 'unknown',
        user_agent: session.user_agent || 'unknown',
        created_at: session.created_at,
        last_used_at: session.last_used_at || session.created_at,
        expires_at: session.refresh_expires_at,
        is_current: false, // Will be updated below
      }));

      // Get current session from cookie
      const refreshToken = await getSignedCookie(context, this.cookieSecret, 'refresh_token');
      if (refreshToken) {
        const currentSession = await this.identityService.findTokenByRefreshToken(refreshToken);
        if (currentSession) {
          // Mark current session
          formattedSessions.forEach((session) => {
            if (session.id === currentSession.id) {
              session.is_current = true;
            }
          });
        }
      }

      return this.success(context, { sessions: formattedSessions });
    } catch (error: any) {
      logger.error('Get sessions error', { error: error.message, userId });
      throw error;
    }
  }

  /**
   * Revoke a specific session
   */
  @HttpPost('/sessions/:sessionId/revoke')
  async revokeSession(@HttpContext() context: Context): Promise<Response> {
    const userId = await this.getCurrentUser(context);
    const sessionId = context.req.param('sessionId');

    try {
      // Find tokens with the session ID
      const sessionTokens = await this.identityService.findSessionTokens(sessionId);

      // Validate session belongs to user
      const isValidSession = sessionTokens.some((token) => token.user_id === userId);
      if (!isValidSession || sessionTokens.length === 0) {
        throw new NotFoundError('Session not found or does not belong to user');
      }

      // Get current session from cookie to prevent revoking own session
      const refreshToken = await getSignedCookie(context, this.cookieSecret, 'refresh_token');
      if (refreshToken) {
        const currentSession = await this.identityService.findTokenByRefreshToken(refreshToken);
        if (currentSession && currentSession.session_id === sessionId) {
          throw new UnauthorizedError('Cannot revoke your current session. Use logout instead.');
        }
      }

      // Revoke all tokens for the session
      let revokedCount = 0;
      for (const token of sessionTokens) {
        // Revoke both access token and refresh token
        if (token.token) {
          await this.identityService.revokeToken(token.token);
          revokedCount++;
        }
        if (token.refresh_token) {
          await this.identityService.revokeRefreshToken(token.refresh_token);
          revokedCount++;
        }
      }

      logger.info('Session revoked', { userId, sessionId, revokedCount });

      return this.success(context, {
        success: true,
        session_id: sessionId,
        revoked_tokens: revokedCount,
      });
    } catch (error: any) {
      logger.error('Revoke session error', { error: error.message, userId, sessionId });
      throw error;
    }
  }

  /**
   * Verify token endpoint - check if token is valid
   */
  @HttpPost('/verify-token')
  @AllowAnonymous()
  async verifyToken(@HttpContext() context: Context): Promise<Response> {
    try {
      const { token } = await context.req.json();

      this.validateIf(!token, 'Token is required');

      // Verify token from database
      const tokenDoc = await this.identityService.verifyTokenFromDb(token);

      if (!tokenDoc) {
        throw new UnauthorizedError('Invalid or expired token');
      }

      // Verify JWT token
      const payload = this.identityService.verifyToken(token);

      if (!payload) {
        throw new UnauthorizedError('Invalid token format');
      }

      return this.success(context, {
        valid: true,
        payload: {
          user_id: payload.sub,
          username: payload.username,
          role: payload.role,
          unit_id: payload.unit_id,
          member_role_id: payload.member_role_id,
          roles: payload.roles,
          permissions: payload.permissions,
          expires_at: new Date(payload.exp * 1000),
        },
      });
    } catch (error: any) {
      logger.error('Token verification error', { error: error.message });
      throw error;
    }
  }
}
