/**
 * Controller Policy Mappings
 * Maps controller methods to their corresponding policies from @c-cam/shared
 */

import { ACTIONS } from '@c-cam/shared';

export interface ControllerPolicyMapping {
  classPolicy: string;
  methods: {
    [methodName: string]: {
      policies: string[];
      resource: string;
      action: string;
      required?: boolean;
    };
  };
}

/**
 * Helper function to normalize controller name (handles both string and constructor.name)
 */
function normalizeControllerName(controllerName: string): string {
  // If it's already a proper controller name, return as is
  if (controllerName.endsWith('Controller')) {
    return controllerName;
  }

  // Handle cases where .name might return just the class name
  return controllerName + 'Controller';
}

/**
 * Helper function to get policy configuration for a controller method
 */
export function getMethodPolicy(controllerName: string, methodName: string) {
  const normalizedName = normalizeControllerName(controllerName);
  const controllerConfig = CONTROLLER_POLICIES[normalizedName];
  if (!controllerConfig) {
    console.warn(`No policy configuration found for controller: ${normalizedName}`);
    throw new Error(`No policy configuration found for controller: ${normalizedName}`);
  }

  const methodConfig = controllerConfig.methods[methodName];
  if (!methodConfig) {
    console.warn(`No policy configuration found for method: ${normalizedName}.${methodName}`);
    throw new Error(`No policy configuration found for method: ${normalizedName}.${methodName}`);
  }

  return methodConfig;
}

/**
 * Helper function to get class policy for a controller
 */
export function getClassPolicy(controllerName: string) {
  const normalizedName = normalizeControllerName(controllerName);
  const controllerConfig = CONTROLLER_POLICIES[normalizedName];
  if (!controllerConfig) {
    throw new Error(`No policy configuration found for controller: ${normalizedName}`);
  }

  return controllerConfig.classPolicy;
}

/**
 * Helper function to create @Authorized decorator options from policy config
 * Supports both string and function.name for method names
 */
export function createAuthorizedOptions(controllerName: string, methodName: string | Function) {
  const actualMethodName = typeof methodName === 'function' ? methodName.name : methodName;
  const config = getMethodPolicy(controllerName, actualMethodName);
  return {
    policies: config.policies,
    resource: config.resource,
    action: config.action,
    required: config.required ?? true,
  };
}

/**
 * Type-safe helper to get method name from function reference
 * Usage: createAuthorizedOptionsFromMethod(ControllerName.name, ControllerName.prototype.methodName)
 */
export function createAuthorizedOptionsFromMethod(controllerName: string, methodRef: Function) {
  return createAuthorizedOptions(controllerName, methodRef.name);
}

/**
 * Utility function to create a method reference for type safety
 * Usage: methodRef<ControllerClass>('methodName')
 */
export function methodRef<T>(methodName: keyof T): string {
  return methodName as string;
}

export const CONTROLLER_POLICIES: Record<string, ControllerPolicyMapping> = {
  CameraController: {
    classPolicy: 'camera-access',
    methods: {
      getCameras: {
        policies: ['camera-list'],
        resource: 'cameras',
        action: ACTIONS.READ,
      },
      getCamerasByType: {
        policies: ['camera-view-by-type'],
        resource: 'cameras/type',
        action: ACTIONS.READ,
      },
      getCamerasByStatus: {
        policies: ['camera-view-by-status'],
        resource: 'cameras/status',
        action: ACTIONS.READ,
      },
      getCamerasByLocation: {
        policies: ['camera-view-by-location'],
        resource: 'cameras/location',
        action: ACTIONS.READ,
      },
      getCameraByIpAddress: {
        policies: ['camera-view-by-ip'],
        resource: 'cameras/ip',
        action: ACTIONS.READ,
      },
      getCameraById: {
        policies: ['camera-view-details'],
        resource: 'cameras/:id',
        action: ACTIONS.READ,
      },
      createCamera: {
        policies: ['camera-create'],
        resource: 'cameras',
        action: ACTIONS.CREATE,
      },
      updateCamera: {
        policies: ['camera-update'],
        resource: 'cameras/:id',
        action: ACTIONS.UPDATE,
      },
      deleteCamera: {
        policies: ['camera-delete'],
        resource: 'cameras/:id',
        action: ACTIONS.DELETE,
      },
    },
  },

  UsersController: {
    classPolicy: 'user-access',
    methods: {
      getUsers: {
        policies: ['user-list'],
        resource: 'users',
        action: ACTIONS.READ,
      },
      getUsersByUnitId: {
        policies: ['user-view-by-unit'],
        resource: 'users/unit',
        action: ACTIONS.READ,
      },
      getUsersByMemberRoleId: {
        policies: ['user-view-by-role'],
        resource: 'users/role',
        action: ACTIONS.READ,
      },
      authenticate: {
        policies: ['user-authenticate'],
        resource: 'users/authenticate',
        action: ACTIONS.CREATE,
        required: false,
      },
      getUserById: {
        policies: ['user-view-details'],
        resource: 'users/:id',
        action: ACTIONS.READ,
      },
      createUser: {
        policies: ['user-create'],
        resource: 'users',
        action: ACTIONS.CREATE,
      },
      updateUser: {
        policies: ['user-update'],
        resource: 'users/:id',
        action: ACTIONS.UPDATE,
      },
      deleteUser: {
        policies: ['user-delete'],
        resource: 'users/:id',
        action: ACTIONS.DELETE,
      },
      uploadAvatar: {
        policies: ['user-upload-avatar'],
        resource: 'users/:id/avatar',
        action: ACTIONS.UPDATE,
      },
      updateUsersStatus: {
        policies: ['user-update-status'],
        resource: 'users/bulk-update-status',
        action: ACTIONS.UPDATE,
      },
    },
  },

  RoleController: {
    classPolicy: 'role-access',
    methods: {
      getRoles: {
        policies: ['role-list'],
        resource: 'roles',
        action: ACTIONS.READ,
      },
      getRoleById: {
        policies: ['role-view-details'],
        resource: 'roles/:id',
        action: ACTIONS.READ,
      },
      createRole: {
        policies: ['role-create'],
        resource: 'roles',
        action: ACTIONS.CREATE,
      },
      updateRole: {
        policies: ['role-update'],
        resource: 'roles/:id',
        action: ACTIONS.UPDATE,
      },
      deleteRole: {
        policies: ['role-delete'],
        resource: 'roles/:id',
        action: ACTIONS.DELETE,
      },
      getRoleByName: {
        policies: ['role-view-by-name'],
        resource: 'roles/name',
        action: ACTIONS.READ,
      },
      getRolesByMemberRoleId: {
        policies: ['role-view-by-member-role'],
        resource: 'roles/member-role',
        action: ACTIONS.READ,
      },
      getRolesByPermissionId: {
        policies: ['role-view-by-permission'],
        resource: 'roles/permission',
        action: ACTIONS.READ,
      },
      getRolesByCreator: {
        policies: ['role-view-by-creator'],
        resource: 'roles/created-by',
        action: ACTIONS.READ,
      },
    },
  },

  PermissionController: {
    classPolicy: 'permission-access',
    methods: {
      getPermissions: {
        policies: ['permission-list'],
        resource: 'permissions',
        action: ACTIONS.READ,
      },
      getPermissionById: {
        policies: ['permission-view-details'],
        resource: 'permissions/:id',
        action: ACTIONS.READ,
      },
      createPermission: {
        policies: ['permission-create'],
        resource: 'permissions',
        action: ACTIONS.CREATE,
      },
      deletePermission: {
        policies: ['permission-delete'],
        resource: 'permissions/:id',
        action: ACTIONS.DELETE,
      },
      checkPermission: {
        policies: ['permission-check'],
        resource: 'permissions/check',
        action: ACTIONS.READ,
      },
      getPermissionsForRole: {
        policies: ['permission-view-by-role'],
        resource: 'permissions/role',
        action: ACTIONS.READ,
      },
      getPermissionsForModule: {
        policies: ['permission-view-by-module'],
        resource: 'permissions/module',
        action: ACTIONS.READ,
      },
      getPermissionsForFeature: {
        policies: ['permission-view-by-feature'],
        resource: 'permissions/feature',
        action: ACTIONS.READ,
      },
      getPermissionsForAction: {
        policies: ['permission-view-by-action'],
        resource: 'permissions/action',
        action: ACTIONS.READ,
      },
      removeAllPermissionsForRole: {
        policies: ['permission-remove-all-for-role'],
        resource: 'permissions/role',
        action: ACTIONS.DELETE,
      },
    },
  },

  MemberRoleController: {
    classPolicy: 'member-role-access',
    methods: {
      getMemberRoles: {
        policies: ['member-role-list'],
        resource: 'member-roles',
        action: ACTIONS.READ,
      },
      getMemberRoleById: {
        policies: ['member-role-view-details'],
        resource: 'member-roles/:id',
        action: ACTIONS.READ,
      },
      assignRoleToUser: {
        policies: ['member-role-assign'],
        resource: 'member-roles/assign',
        action: ACTIONS.CREATE,
      },
      removeRoleFromUser: {
        policies: ['member-role-remove'],
        resource: 'member-roles/user/role',
        action: ACTIONS.DELETE,
      },
      getRolesForUser: {
        policies: ['member-role-view-by-user'],
        resource: 'member-roles/user',
        action: ACTIONS.READ,
      },
      getUsersWithRole: {
        policies: ['member-role-view-by-role'],
        resource: 'member-roles/role',
        action: ACTIONS.READ,
      },
      removeAllRolesFromUser: {
        policies: ['member-role-remove-all-from-user'],
        resource: 'member-roles/user/roles',
        action: ACTIONS.DELETE,
      },
      removeRoleFromAllUsers: {
        policies: ['member-role-remove-from-all-users'],
        resource: 'member-roles/role/users',
        action: ACTIONS.DELETE,
      },
    },
  },

  EdgeDeviceController: {
    classPolicy: 'edge-device-access',
    methods: {
      getEdgeDevices: {
        policies: ['edge-device-list'],
        resource: 'edge-devices',
        action: ACTIONS.READ,
      },
      getAttendanceDevices: {
        policies: ['edge-device-view-attendance'],
        resource: 'edge-devices/attendance',
        action: ACTIONS.READ,
      },
      getEdgeDevicesByCameraId: {
        policies: ['edge-device-view-by-camera'],
        resource: 'edge-devices/camera',
        action: ACTIONS.READ,
      },
      getEdgeDevicesByType: {
        policies: ['edge-device-view-by-type'],
        resource: 'edge-devices/type',
        action: ACTIONS.READ,
      },
      getEdgeDevicesByStatus: {
        policies: ['edge-device-view-by-status'],
        resource: 'edge-devices/status',
        action: ACTIONS.READ,
      },
      getEdgeDeviceByIpAddress: {
        policies: ['edge-device-view-by-ip'],
        resource: 'edge-devices/ip',
        action: ACTIONS.READ,
      },
      getEdgeDeviceByMacAddress: {
        policies: ['edge-device-view-by-mac'],
        resource: 'edge-devices/mac',
        action: ACTIONS.READ,
      },
      getEdgeDevicesByFirmwareVersion: {
        policies: ['edge-device-view-by-firmware'],
        resource: 'edge-devices/firmware',
        action: ACTIONS.READ,
      },
      getEdgeDeviceById: {
        policies: ['edge-device-view-details'],
        resource: 'edge-devices/:id',
        action: ACTIONS.READ,
      },
      createEdgeDevice: {
        policies: ['edge-device-create'],
        resource: 'edge-devices',
        action: ACTIONS.CREATE,
      },
      updateEdgeDevice: {
        policies: ['edge-device-update'],
        resource: 'edge-devices/:id',
        action: ACTIONS.UPDATE,
      },
      deleteEdgeDevice: {
        policies: ['edge-device-delete'],
        resource: 'edge-devices/:id',
        action: ACTIONS.DELETE,
      },
    },
  },

  PolicyController: {
    classPolicy: 'policy-access',
    methods: {
      getPolicies: {
        policies: ['policy-list'],
        resource: 'policies',
        action: ACTIONS.READ,
      },
      getPolicyById: {
        policies: ['policy-view-details'],
        resource: 'policies/:id',
        action: ACTIONS.READ,
      },
      createPolicy: {
        policies: ['policy-create'],
        resource: 'policies',
        action: ACTIONS.CREATE,
      },
      updatePolicy: {
        policies: ['policy-update'],
        resource: 'policies/:id',
        action: ACTIONS.UPDATE,
      },
      deletePolicy: {
        policies: ['policy-delete'],
        resource: 'policies/:id',
        action: ACTIONS.DELETE,
      },
    },
  },

  IdentityController: {
    classPolicy: 'identity-access',
    methods: {
      logout: {
        policies: ['identity-logout'],
        resource: 'identity/logout',
        action: ACTIONS.CREATE,
      },
      verifyToken: {
        policies: ['identity-verify-token'],
        resource: 'identity/verify-token',
        action: ACTIONS.READ,
        required: false,
      },
      getMe: {
        policies: ['identity-profile'],
        resource: 'identity/me',
        action: ACTIONS.READ,
      },
      updateProfile: {
        policies: ['identity-update-profile'],
        resource: 'identity/me',
        action: ACTIONS.UPDATE,
      },
      logoutAll: {
        policies: ['identity-logout-all'],
        resource: 'identity/logout-all',
        action: ACTIONS.CREATE,
      },
      getSessions: {
        policies: ['identity-sessions'],
        resource: 'identity/sessions',
        action: ACTIONS.READ,
      },
      revokeSession: {
        policies: ['identity-revoke-session'],
        resource: 'identity/sessions/revoke',
        action: ACTIONS.CREATE,
      },
    },
  },

  FaceImagesController: {
    classPolicy: 'face-image-access',
    methods: {
      getFaceImages: {
        policies: ['face-image-list'],
        resource: 'face-images',
        action: ACTIONS.READ,
      },
      getFaceImageById: {
        policies: ['face-image-view-details'],
        resource: 'face-images/:id',
        action: ACTIONS.READ,
      },
      createFaceImage: {
        policies: ['face-image-create'],
        resource: 'face-images',
        action: ACTIONS.CREATE,
      },
      updateFaceImage: {
        policies: ['face-image-update'],
        resource: 'face-images/:id',
        action: ACTIONS.UPDATE,
      },
      deleteFaceImage: {
        policies: ['face-image-delete'],
        resource: 'face-images/:id',
        action: ACTIONS.DELETE,
      },
      getFaceImagesByUserId: {
        policies: ['face-image-view-by-user'],
        resource: 'face-images/user',
        action: ACTIONS.READ,
      },
      getFaceImagesByImageAngle: {
        policies: ['face-image-view-by-angle'],
        resource: 'face-images/angle',
        action: ACTIONS.READ,
      },
      getFaceImagesByDateRange: {
        policies: ['face-image-view-by-date-range'],
        resource: 'face-images/date-range',
        action: ACTIONS.READ,
      },
      getFaceImagesByUserIdAndImageAngle: {
        policies: ['face-image-view-by-user-angle'],
        resource: 'face-images/user/angle',
        action: ACTIONS.READ,
      },
      deleteFaceImagesByUserId: {
        policies: ['face-image-delete-by-user'],
        resource: 'face-images/user',
        action: ACTIONS.DELETE,
      },
      getLatestFrontImage: {
        policies: ['face-image-view-latest-front'],
        resource: 'face-images/user/latest-front',
        action: ACTIONS.READ,
      },
    },
  },

  UnitController: {
    classPolicy: 'unit-access',
    methods: {
      getUnits: {
        policies: ['unit-list'],
        resource: 'units',
        action: ACTIONS.READ,
      },
      getUnitById: {
        policies: ['unit-view-details'],
        resource: 'units/:id',
        action: ACTIONS.READ,
      },
      createUnit: {
        policies: ['unit-create'],
        resource: 'units',
        action: ACTIONS.CREATE,
      },
      updateUnit: {
        policies: ['unit-update'],
        resource: 'units/:id',
        action: ACTIONS.UPDATE,
      },
      deleteUnit: {
        policies: ['unit-delete'],
        resource: 'units/:id',
        action: ACTIONS.DELETE,
      },
      getUnitsByParentId: {
        policies: ['unit-view-by-parent'],
        resource: 'units/parent',
        action: ACTIONS.READ,
      },
      getUnitsByType: {
        policies: ['unit-view-by-type'],
        resource: 'units/type',
        action: ACTIONS.READ,
      },
      getUnitHierarchy: {
        policies: ['unit-view-hierarchy'],
        resource: 'units/hierarchy',
        action: ACTIONS.READ,
      },
      getRootUnits: {
        policies: ['unit-view-roots'],
        resource: 'units/roots',
        action: ACTIONS.READ,
      },
      updateUsersUnit: {
        policies: ['unit-update-users'],
        resource: 'units/users/bulk-update',
        action: ACTIONS.UPDATE,
      },
    },
  },

  TenantController: {
    classPolicy: 'tenant-access',
    methods: {
      getTenants: {
        policies: ['tenant-list'],
        resource: 'tenants',
        action: ACTIONS.READ,
      },
      getTenantById: {
        policies: ['tenant-view-details'],
        resource: 'tenants/:id',
        action: ACTIONS.READ,
      },
      createTenant: {
        policies: ['tenant-create'],
        resource: 'tenants',
        action: ACTIONS.CREATE,
      },
      updateTenant: {
        policies: ['tenant-update'],
        resource: 'tenants/:id',
        action: ACTIONS.UPDATE,
      },
      deleteTenant: {
        policies: ['tenant-delete'],
        resource: 'tenants/:id',
        action: ACTIONS.DELETE,
      },
      getTenantByName: {
        policies: ['tenant-view-by-name'],
        resource: 'tenants/name',
        action: ACTIONS.READ,
      },
      getTenantsByStatus: {
        policies: ['tenant-view-by-status'],
        resource: 'tenants/status',
        action: ACTIONS.READ,
      },
      activateTenant: {
        policies: ['tenant-activate'],
        resource: 'tenants/activate',
        action: ACTIONS.UPDATE,
      },
      deactivateTenant: {
        policies: ['tenant-deactivate'],
        resource: 'tenants/deactivate',
        action: ACTIONS.UPDATE,
      },
    },
  },

  ShiftController: {
    classPolicy: 'shift-access',
    methods: {
      getShifts: {
        policies: ['shift-list'],
        resource: 'shifts',
        action: ACTIONS.READ,
      },
      getShiftById: {
        policies: ['shift-view-details'],
        resource: 'shifts/:id',
        action: ACTIONS.READ,
      },
      createShift: {
        policies: ['shift-create'],
        resource: 'shifts',
        action: ACTIONS.CREATE,
      },
      createShiftWithDetail: {
        policies: ['shift-create-with-detail'],
        resource: 'shifts/with-detail',
        action: ACTIONS.CREATE,
      },
      updateShift: {
        policies: ['shift-update'],
        resource: 'shifts/:id',
        action: ACTIONS.UPDATE,
      },
      deleteShift: {
        policies: ['shift-delete'],
        resource: 'shifts/:id',
        action: ACTIONS.DELETE,
      },
      getShiftsByDate: {
        policies: ['shift-view-by-date'],
        resource: 'shifts/date',
        action: ACTIONS.READ,
      },
      getShiftsByUser: {
        policies: ['shift-view-by-user'],
        resource: 'shifts/user',
        action: ACTIONS.READ,
      },
      getActiveShifts: {
        policies: ['shift-view-active'],
        resource: 'shifts/active',
        action: ACTIONS.READ,
      },
      getShiftByName: {
        policies: ['shift-view-by-name'],
        resource: 'shifts/name',
        action: ACTIONS.READ,
      },
      getShiftsByShiftType: {
        policies: ['shift-view-by-type'],
        resource: 'shifts/type',
        action: ACTIONS.READ,
      },
      getShiftsByWorkCoefficient: {
        policies: ['shift-view-by-coefficient'],
        resource: 'shifts/coefficient',
        action: ACTIONS.READ,
      },
    },
  },

  ShiftDetailController: {
    classPolicy: 'shift-detail-access',
    methods: {
      getShiftDetails: {
        policies: ['shift-detail-list'],
        resource: 'shift-details',
        action: ACTIONS.READ,
      },
      getShiftDetailById: {
        policies: ['shift-detail-view-details'],
        resource: 'shift-details/:id',
        action: ACTIONS.READ,
      },
      createShiftDetail: {
        policies: ['shift-detail-create'],
        resource: 'shift-details',
        action: ACTIONS.CREATE,
      },
      updateShiftDetail: {
        policies: ['shift-detail-update'],
        resource: 'shift-details/:id',
        action: ACTIONS.UPDATE,
      },
      deleteShiftDetail: {
        policies: ['shift-detail-delete'],
        resource: 'shift-details/:id',
        action: ACTIONS.DELETE,
      },
      getShiftDetailsByShift: {
        policies: ['shift-detail-view-by-shift'],
        resource: 'shift-details/shift',
        action: ACTIONS.READ,
      },
      getShiftDetailsByUser: {
        policies: ['shift-detail-view-by-user'],
        resource: 'shift-details/user',
        action: ACTIONS.READ,
      },
    },
  },

  DailyAttendanceSummariesController: {
    classPolicy: 'daily-attendance-summary-access',
    methods: {
      getAttendanceSummaries: {
        policies: ['daily-attendance-summary-list'],
        resource: 'daily-attendance-summaries',
        action: ACTIONS.READ,
      },
      getLateAttendanceSummaries: {
        policies: ['daily-attendance-summary-view-late'],
        resource: 'daily-attendance-summaries/late',
        action: ACTIONS.READ,
      },
      getEarlyLeaveAttendanceSummaries: {
        policies: ['daily-attendance-summary-view-early-leave'],
        resource: 'daily-attendance-summaries/early-leave',
        action: ACTIONS.READ,
      },
      getAttendanceSummariesByUserId: {
        policies: ['daily-attendance-summary-view-by-user'],
        resource: 'daily-attendance-summaries/user',
        action: ACTIONS.READ,
      },
      getAttendanceSummariesByUserIdAndDateRange: {
        policies: ['daily-attendance-summary-view-by-user-date-range'],
        resource: 'daily-attendance-summaries/user/date-range',
        action: ACTIONS.READ,
      },
      getAttendanceSummariesByShiftId: {
        policies: ['daily-attendance-summary-view-by-shift'],
        resource: 'daily-attendance-summaries/shift',
        action: ACTIONS.READ,
      },
      getAttendanceSummariesByHolidayId: {
        policies: ['daily-attendance-summary-view-by-holiday'],
        resource: 'daily-attendance-summaries/holiday',
        action: ACTIONS.READ,
      },
      getAttendanceSummariesByWorkDate: {
        policies: ['daily-attendance-summary-view-by-date'],
        resource: 'daily-attendance-summaries/date',
        action: ACTIONS.READ,
      },
      getAttendanceSummariesByDateRange: {
        policies: ['daily-attendance-summary-view-by-date-range'],
        resource: 'daily-attendance-summaries/date-range',
        action: ACTIONS.READ,
      },
      getAttendanceSummaryById: {
        policies: ['daily-attendance-summary-view-details'],
        resource: 'daily-attendance-summaries/:id',
        action: ACTIONS.READ,
      },
      createAttendanceSummary: {
        policies: ['daily-attendance-summary-create'],
        resource: 'daily-attendance-summaries',
        action: ACTIONS.CREATE,
      },
      updateAttendanceSummary: {
        policies: ['daily-attendance-summary-update'],
        resource: 'daily-attendance-summaries/:id',
        action: ACTIONS.UPDATE,
      },
      deleteAttendanceSummary: {
        policies: ['daily-attendance-summary-delete'],
        resource: 'daily-attendance-summaries/:id',
        action: ACTIONS.DELETE,
      },
    },
  },

  EdgeDeviceLogsController: {
    classPolicy: 'edge-device-log-access',
    methods: {
      getEdgeDeviceLogs: {
        policies: ['edge-device-log-list'],
        resource: 'edge-device-logs',
        action: ACTIONS.READ,
      },
      getEdgeDeviceLogById: {
        policies: ['edge-device-log-view-details'],
        resource: 'edge-device-logs/:id',
        action: ACTIONS.READ,
      },
      createEdgeDeviceLog: {
        policies: ['edge-device-log-create'],
        resource: 'edge-device-logs',
        action: ACTIONS.CREATE,
      },
      deleteEdgeDeviceLog: {
        policies: ['edge-device-log-delete'],
        resource: 'edge-device-logs/:id',
        action: ACTIONS.DELETE,
      },
      getLogsByEdgeDeviceId: {
        policies: ['edge-device-log-view-by-device'],
        resource: 'edge-device-logs/device',
        action: ACTIONS.READ,
      },
      getLogsByLogLevel: {
        policies: ['edge-device-log-view-by-level'],
        resource: 'edge-device-logs/level',
        action: ACTIONS.READ,
      },
      getLogsByDateRange: {
        policies: ['edge-device-log-view-by-date-range'],
        resource: 'edge-device-logs/date-range',
        action: ACTIONS.READ,
      },
      getLogsByEdgeDeviceIdAndDateRange: {
        policies: ['edge-device-log-view-by-device-date-range'],
        resource: 'edge-device-logs/device/date-range',
        action: ACTIONS.READ,
      },
      getLogsByEdgeDeviceIdAndLogLevel: {
        policies: ['edge-device-log-view-by-device-level'],
        resource: 'edge-device-logs/device/level',
        action: ACTIONS.READ,
      },
      deleteLogsByEdgeDeviceId: {
        policies: ['edge-device-log-delete-by-device'],
        resource: 'edge-device-logs/device',
        action: ACTIONS.DELETE,
      },
    },
  },

  EdgeDeviceInfoController: {
    classPolicy: 'edge-device-info-access',
    methods: {
      getEdgeDeviceInfo: {
        policies: ['edge-device-info-list'],
        resource: 'edge-device-infos',
        action: ACTIONS.READ,
      },
      getEdgeDeviceInfoById: {
        policies: ['edge-device-info-view-details'],
        resource: 'edge-device-infos/:id',
        action: ACTIONS.READ,
      },
      createEdgeDeviceInfo: {
        policies: ['edge-device-info-create'],
        resource: 'edge-device-infos',
        action: ACTIONS.CREATE,
      },
      updateEdgeDeviceInfo: {
        policies: ['edge-device-info-update'],
        resource: 'edge-device-infos/:id',
        action: ACTIONS.UPDATE,
      },
      deleteEdgeDeviceInfo: {
        policies: ['edge-device-info-delete'],
        resource: 'edge-device-infos/:id',
        action: ACTIONS.DELETE,
      },
      getEdgeDeviceInfoByDeviceId: {
        policies: ['edge-device-info-view-by-device'],
        resource: 'edge-device-infos/device',
        action: ACTIONS.READ,
      },
      getEdgeDeviceInfoByCpuModel: {
        policies: ['edge-device-info-view-by-cpu'],
        resource: 'edge-device-infos/cpu',
        action: ACTIONS.READ,
      },
      getEdgeDeviceInfoByOs: {
        policies: ['edge-device-info-view-by-os'],
        resource: 'edge-device-infos/os',
        action: ACTIONS.READ,
      },
      getEdgeDeviceInfoByDateRange: {
        policies: ['edge-device-info-view-by-date-range'],
        resource: 'edge-device-infos/date-range',
        action: ACTIONS.READ,
      },
      deleteEdgeDeviceInfoByDeviceId: {
        policies: ['edge-device-info-delete-by-device'],
        resource: 'edge-device-infos/device',
        action: ACTIONS.DELETE,
      },
    },
  },

  FaceRecognitionLogsController: {
    classPolicy: 'face-recognition-log-access',
    methods: {
      getFaceRecognitionLogs: {
        policies: ['face-recognition-log-list'],
        resource: 'face-recognition-logs',
        action: ACTIONS.READ,
      },
      getFaceRecognitionLogById: {
        policies: ['face-recognition-log-view-details'],
        resource: 'face-recognition-logs/:id',
        action: ACTIONS.READ,
      },
      logRecognitionEvent: {
        policies: ['face-recognition-log-create'],
        resource: 'face-recognition-logs',
        action: ACTIONS.CREATE,
      },
      deleteFaceRecognitionLog: {
        policies: ['face-recognition-log-delete'],
        resource: 'face-recognition-logs/:id',
        action: ACTIONS.DELETE,
      },
      getLogsByUserId: {
        policies: ['face-recognition-log-view-by-user'],
        resource: 'face-recognition-logs/user',
        action: ACTIONS.READ,
      },
      getLogsByCameraId: {
        policies: ['face-recognition-log-view-by-camera'],
        resource: 'face-recognition-logs/camera',
        action: ACTIONS.READ,
      },
      getLogsByEdgeDeviceId: {
        policies: ['face-recognition-log-view-by-device'],
        resource: 'face-recognition-logs/device',
        action: ACTIONS.READ,
      },
      getLogsByStatus: {
        policies: ['face-recognition-log-view-by-status'],
        resource: 'face-recognition-logs/status',
        action: ACTIONS.READ,
      },
      getLogsBySimilarityRange: {
        policies: ['face-recognition-log-view-by-similarity'],
        resource: 'face-recognition-logs/similarity',
        action: ACTIONS.READ,
      },
      getLogsByDateRange: {
        policies: ['face-recognition-log-view-by-date-range'],
        resource: 'face-recognition-logs/date-range',
        action: ACTIONS.READ,
      },
      getLogsByUserIdAndDateRange: {
        policies: ['face-recognition-log-view-by-user-date-range'],
        resource: 'face-recognition-logs/user/date-range',
        action: ACTIONS.READ,
      },
      generateUserRecognitionSummary: {
        policies: ['face-recognition-log-generate-summary'],
        resource: 'face-recognition-logs/user/summary',
        action: ACTIONS.READ,
      },
    },
  },

  StorageController: {
    classPolicy: 'storage-access',
    methods: {
      // Bucket Management Methods
      listBuckets: {
        policies: ['storage-bucket-list'],
        resource: 'storage/buckets',
        action: ACTIONS.READ,
      },
      getBucketById: {
        policies: ['storage-bucket-view-details'],
        resource: 'storage/buckets/:id',
        action: ACTIONS.READ,
      },
      createBucket: {
        policies: ['storage-bucket-create'],
        resource: 'storage/buckets',
        action: ACTIONS.CREATE,
      },
      updateBucket: {
        policies: ['storage-bucket-update'],
        resource: 'storage/buckets/:id',
        action: ACTIONS.UPDATE,
      },
      deleteBucket: {
        policies: ['storage-bucket-delete'],
        resource: 'storage/buckets/:id',
        action: ACTIONS.DELETE,
      },
      // File Management Methods
      generateUploadUrl: {
        policies: ['storage-file-upload-url'],
        resource: 'storage/buckets/upload-url',
        action: ACTIONS.CREATE,
      },
      generateDownloadUrl: {
        policies: ['storage-file-download-url'],
        resource: 'storage/buckets/download-url',
        action: ACTIONS.READ,
      },
      streamFile: {
        policies: ['storage-file-stream'],
        resource: 'storage/files/:fileId/stream',
        action: ACTIONS.READ,
      },
    },
  },
};
