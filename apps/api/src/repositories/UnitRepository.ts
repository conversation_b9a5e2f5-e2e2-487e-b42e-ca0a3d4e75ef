import UnitModel, { UnitDocument } from '@/database/entities/UnitModel';
import { Injectable, Repository } from '@c-cam/core';

/**
 * Repository for managing organizational units
 * Extends the BaseRepository with UnitDocument type
 */
@Injectable()
class UnitRepository extends Repository<UnitDocument> {
  constructor() {
    super(UnitModel);
  }

  /**
   * Find units by organization ID
   * @param organizationId The organization ID to search for
   * @returns A promise that resolves to an array of units
   */
  async findByOrganizationId(organizationId: string): Promise<UnitDocument[]> {
    return this.find({ organization_id: organizationId });
  }

  /**
   * Find units by user ID
   * @param userId The user ID to search for
   * @returns A promise that resolves to an array of units
   */
  async findByUserId(userId: string): Promise<UnitDocument[]> {
    return this.find({ user_id: userId });
  }

  /**
   * Find units by parent unit ID
   * @param parentUnitId The parent unit ID to search for
   * @returns A promise that resolves to an array of units
   */
  async findByParentUnitId(parentUnitId: string): Promise<UnitDocument[]> {
    return this.find({ parent_unit_id: parentUnitId });
  }

  /**
   * Find a unit by name and organization ID
   * @param name The unit name to search for
   * @param organizationId The organization ID to search for
   * @returns A promise that resolves to a unit or null if not found
   */
  async findByNameAndOrganizationId(
    name: string,
    organizationId: string,
  ): Promise<UnitDocument | null> {
    return this.findOne({ name, organization_id: organizationId });
  }

  /**
   * Get all root units (units without a parent)
   * @returns A promise that resolves to an array of root units
   */
  async findRootUnits(): Promise<UnitDocument[]> {
    return this.find({ parent_unit_id: { $exists: false } });
  }

  /**
   * Get root units filtered by tenant (units without a parent in a specific organization)
   * @param tenantId The tenant ID to filter by
   * @returns A promise that resolves to an array of root units belonging to the tenant
   */
  async findRootUnitsByTenant(tenantId: string): Promise<UnitDocument[]> {
    return this.find({
      organization_id: tenantId,
      parent_unit_id: { $exists: false },
    });
  }

  /**
   * Find units by tenant ID
   * This method finds units that belong to a specific tenant by looking up
   * units where the organization_id matches the tenantId
   * @param tenantId The tenant ID to search for
   * @returns A promise that resolves to an array of units
   */
  async findByTenantId(tenantId: string): Promise<UnitDocument[]> {
    return this.find({ organization_id: tenantId });
  }
}

export default UnitRepository;
