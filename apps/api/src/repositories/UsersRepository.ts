import { Injectable, Repository } from '@c-cam/core';
import UsersModel, { UsersDocument } from '../database/entities/UsersModel';

/**
 * Repository for managing users
 * Extends the BaseRepository with UsersDocument type
 */
@Injectable()
class UsersRepository extends Repository<UsersDocument> {
  constructor() {
    super(UsersModel);
  }

  /**
   * Find a user by username
   * @param username The username to search for
   * @returns A promise that resolves to a user or null if not found
   */
  async findByUsername(username: string): Promise<UsersDocument | null> {
    return this.findOne({ username });
  }

  async findByUsernameSelectPassword(username: string): Promise<UsersDocument | null> {
    const user = (await this.getModel()
      .findOne({ username })
      .select('+password')
      .exec()) as UsersDocument | null;
    return user;
  }

  /**
   * Find a user by email
   * @param email The email to search for
   * @returns A promise that resolves to a user or null if not found
   */
  async findByEmail(email: string): Promise<UsersDocument | null> {
    return this.findOne({ email });
  }

  /**
   * Find a user by code
   * @param code The user code to search for
   * @returns A promise that resolves to a user or null if not found
   */
  async findByCode(code: string): Promise<UsersDocument | null> {
    return this.findOne({ code });
  }

  /**
   * Find users by unit ID
   * @param unitId The unit ID to search for
   * @returns A promise that resolves to an array of users
   */
  async findByUnitId(unitId: string): Promise<UsersDocument[]> {
    return this.find({ unit_id: unitId });
  }

  /**
   * Find users by member role ID
   * @param memberRoleId The member role ID to search for
   * @returns A promise that resolves to an array of users
   */
  async findByMemberRoleId(memberRoleId: string): Promise<UsersDocument[]> {
    return this.find({ member_role_id: memberRoleId });
  }

  /**
   * Find users by face ID
   * @param faceId The face ID to search for
   * @returns A promise that resolves to an array of users
   */
  async findByFaceId(faceId: string): Promise<UsersDocument[]> {
    return this.find({ face_id: faceId });
  }

  /**
   * Find users by tenant ID
   * This method finds users that belong to a specific tenant by:
   * 1. Finding all units that belong to the tenant (organization_id = tenantId)
   * 2. Finding all users that belong to those units
   * @param tenantId The tenant ID to search for
   * @returns A promise that resolves to an array of users
   */
  async findByTenantId(tenantId: string): Promise<UsersDocument[]> {
    // First, get all units that belong to this tenant
    const UnitModel = (await import('../database/entities/UnitModel')).default;
    const units = await UnitModel.find({ organization_id: tenantId }).select('_id');
    const unitIds = units.map(unit => unit._id.toString());

    // Then find all users that belong to those units
    return this.find({ unit_id: { $in: unitIds } });
  }

  /**
   * Find users by unit ID with populated tenant and unit information
   * @param unitId The unit ID to search for
   * @returns A promise that resolves to an array of users with populated data
   */
  async findByUnitIdWithPopulate(unitId: string): Promise<any[]> {
    const UnitModel = (await import('../database/entities/UnitModel')).default;
    const TenantModel = (await import('../database/entities/TenantModel')).default;

    const users = await this.getModel()
      .find({ unit_id: unitId })
      .lean()
      .exec();

    // Populate unit and tenant information
    const populatedUsers = await Promise.all(
      users.map(async (user) => {
        const unit = await UnitModel.findById(user.unit_id).lean().exec();
        let tenant = null;

        if (unit?.organization_id) {
          tenant = await TenantModel.findById(unit.organization_id).lean().exec();
        }

        return {
          ...user,
          unit: unit ? {
            id: unit._id.toString(),
            name: unit.name,
          } : null,
          tenant: tenant ? {
            id: tenant._id.toString(),
            name: tenant.name,
          } : null,
        };
      })
    );

    return populatedUsers;
  }

  /**
   * Find users by unit ID with populated tenant and unit information (paginated)
   * @param unitId The unit ID to search for
   * @param pageSize Number of items per page
   * @param pageIndex Page index (0-based)
   * @returns A promise that resolves to paginated users with populated data
   */
  async findByUnitIdWithPopulatePaginated(
    unitId: string,
    pageSize: number,
    pageIndex: number
  ): Promise<{
    users: any[];
    pageSize: number;
    pageIndex: number;
    totalPages: number;
    totalCount: number;
  }> {
    const UnitModel = (await import('../database/entities/UnitModel')).default;
    const TenantModel = (await import('../database/entities/TenantModel')).default;

    // Get total count
    const totalCount = await this.getModel().countDocuments({ unit_id: unitId });
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get paginated users
    const users = await this.getModel()
      .find({ unit_id: unitId })
      .skip(pageIndex * pageSize)
      .limit(pageSize)
      .lean()
      .exec();

    // Populate unit and tenant information
    const populatedUsers = await Promise.all(
      users.map(async (user) => {
        const unit = await UnitModel.findById(user.unit_id).lean().exec();
        let tenant = null;

        if (unit?.organization_id) {
          tenant = await TenantModel.findById(unit.organization_id).lean().exec();
        }

        return {
          ...user,
          unit: unit ? {
            id: unit._id.toString(),
            name: unit.name,
          } : null,
          tenant: tenant ? {
            id: tenant._id.toString(),
            name: tenant.name,
          } : null,
        };
      })
    );

    return {
      users: populatedUsers,
      pageSize,
      pageIndex,
      totalPages,
      totalCount,
    };
  }

  /**
   * Find users by tenant ID with populated tenant and unit information
   * @param tenantId The tenant ID to search for
   * @returns A promise that resolves to an array of users with populated data
   */
  async findByTenantIdWithPopulate(tenantId: string): Promise<any[]> {
    const UnitModel = (await import('../database/entities/UnitModel')).default;
    const TenantModel = (await import('../database/entities/TenantModel')).default;

    // First, get all units that belong to this tenant
    const units = await UnitModel.find({ organization_id: tenantId }).lean().exec();
    const unitIds = units.map(unit => unit._id.toString());

    // Then find all users that belong to those units
    const users = await this.getModel()
      .find({ unit_id: { $in: unitIds } })
      .lean()
      .exec();

    // Get tenant information
    const tenant = await TenantModel.findById(tenantId).lean().exec();

    // Create unit lookup map for performance
    const unitMap = new Map(units.map(unit => [unit._id.toString(), unit]));

    // Populate user data
    const populatedUsers = users.map((user) => {
      const unit = unitMap.get(user.unit_id);

      return {
        ...user,
        unit: unit ? {
          id: unit._id.toString(),
          name: unit.name,
        } : null,
        tenant: tenant ? {
          id: tenant._id.toString(),
          name: tenant.name,
        } : null,
      };
    });

    return populatedUsers;
  }

  /**
   * Find users by tenant ID with populated tenant and unit information (paginated)
   * @param tenantId The tenant ID to search for
   * @param pageSize Number of items per page
   * @param pageIndex Page index (0-based)
   * @returns A promise that resolves to paginated users with populated data
   */
  async findByTenantIdWithPopulatePaginated(
    tenantId: string,
    pageSize: number,
    pageIndex: number
  ): Promise<{
    users: any[];
    pageSize: number;
    pageIndex: number;
    totalPages: number;
    totalCount: number;
  }> {
    const UnitModel = (await import('../database/entities/UnitModel')).default;
    const TenantModel = (await import('../database/entities/TenantModel')).default;

    // First, get all units that belong to this tenant
    const units = await UnitModel.find({ organization_id: tenantId }).lean().exec();
    const unitIds = units.map(unit => unit._id.toString());

    // Get total count
    const totalCount = await this.getModel().countDocuments({ unit_id: { $in: unitIds } });
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get paginated users
    const users = await this.getModel()
      .find({ unit_id: { $in: unitIds } })
      .skip(pageIndex * pageSize)
      .limit(pageSize)
      .lean()
      .exec();

    // Get tenant information
    const tenant = await TenantModel.findById(tenantId).lean().exec();

    // Create unit lookup map for performance
    const unitMap = new Map(units.map(unit => [unit._id.toString(), unit]));

    // Populate user data
    const populatedUsers = users.map((user) => {
      const unit = unitMap.get(user.unit_id);

      return {
        ...user,
        unit: unit ? {
          id: unit._id.toString(),
          name: unit.name,
        } : null,
        tenant: tenant ? {
          id: tenant._id.toString(),
          name: tenant.name,
        } : null,
      };
    });

    return {
      users: populatedUsers,
      pageSize,
      pageIndex,
      totalPages,
      totalCount,
    };
  }

  /**
   * Generate a unique member code in format E00001, E00002, E00003, etc.
   * @returns A unique member code
   */
  async generateMemberCode(): Promise<string> {
    let attempts = 0;
    const maxAttempts = 100; // Prevent infinite loop

    while (attempts < maxAttempts) {
      // Find the highest existing member code with E prefix
      const lastUser = await this.findOne(
        { code: { $regex: /^E\d{5}$/ } }, // Match codes like E00001, E00002, etc.
        { sort: { code: -1 } } // Sort by code descending to get the highest
      );

      let nextNumber = 1;

      if (lastUser?.code) {
        // Extract number from code (e.g., "E00123" -> 123)
        const match = lastUser.code.match(/^E(\d{5})$/);
        if (match) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }

      // Format as E00001, E00002, etc. (5-digit padding)
      const newCode = `E${nextNumber.toString().padStart(5, '0')}`;

      // Check if this code already exists (double-check for uniqueness)
      const existingUser = await this.findOne({ code: newCode });

      if (!existingUser) {
        return newCode;
      }

      attempts++;
    }

    // Fallback: generate random code if sequential fails
    return this.generateRandomMemberCode();
  }

  /**
   * Generate a random unique member code as fallback
   * @returns A unique random member code
   */
  private async generateRandomMemberCode(): Promise<string> {
    let attempts = 0;
    const maxAttempts = 50;

    while (attempts < maxAttempts) {
      // Generate random 5-digit number
      const randomNumber = Math.floor(Math.random() * 99999) + 1;
      const code = `E${randomNumber.toString().padStart(5, '0')}`;

      // Check if this code already exists
      const existingUser = await this.findOne({ code });

      if (!existingUser) {
        return code;
      }

      attempts++;
    }

    // Ultimate fallback with timestamp
    const timestamp = Date.now().toString().slice(-5);
    return `E${timestamp}`;
  }
}

export default UsersRepository;
