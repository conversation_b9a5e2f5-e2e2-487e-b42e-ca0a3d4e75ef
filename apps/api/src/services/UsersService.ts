import { UsersDocument } from '@/database/entities/UsersModel';
import UsersRepository from '@/repositories/UsersRepository';
import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { UserGender } from '@c-cam/types';
import FilesService from './FilesService';
import StorageService from './StorageService';

/**
 * Service for managing users
 * Extends the BaseModel with UsersDocument type
 */
@Injectable()
class UsersService extends BaseModel<UsersDocument> {
  /**
   * Create a new UsersService
   * @param usersRepository The users repository
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(UsersRepository) private usersRepository: UsersRepository,
    @Inject(StorageService) private storageService: StorageService,
    @Inject(FilesService) private filesService: FilesService,
  ) {
    super(usersRepository);
  }

  /**
   * Create a new user
   * @param userData The user data
   * @returns The newly created user
   */
  async createUser(userData: {
    unit_id: string;
    code: string;
    name: string;
    email?: string;
    phone?: string;
    dob?: Date;
    gender?: UserGender;
    username: string;
    password: string;
    created_by: string;
    face_id?: string;
    member_role_id?: string;
    tenant_id: string;
    current_tenant_id?: string;
    affiliated_tenants?: string[];
  }): Promise<UsersDocument> {
    // Validate email format if provided
    if (userData.email && !userData.email.includes('@')) {
      throw new Error('Invalid email format');
    }

    // Check if username already exists
    const existingUsername = await this.usersRepository.findByUsername(userData.username);
    if (existingUsername) {
      throw new Error(`Username '${userData.username}' is already taken`);
    }

    // Check if email already exists (if provided)
    if (userData.email) {
      const existingEmail = await this.usersRepository.findByEmail(userData.email);
      if (existingEmail) {
        throw new Error(`Email '${userData.email}' is already registered`);
      }
    }

    // Check if code already exists
    const existingCode = await this.usersRepository.findByCode(userData.code);
    if (existingCode) {
      throw new Error(`User code '${userData.code}' is already in use`);
    }

    // Set default values for tenant fields
    const affiliatedTenants = userData.affiliated_tenants || [userData.tenant_id];
    const currentTenantId = userData.current_tenant_id || userData.tenant_id;

    // Create the new user with proper gender typing and tenant fields
    const userDataTyped = {
      ...userData,
      gender: userData.gender ? (userData.gender as UserGender) : undefined,
      affiliated_tenants: affiliatedTenants,
      current_tenant_id: currentTenantId,
    };
    return this.create(userDataTyped);
  }

  /**
   * Update a user
   * @param id The user ID
   * @param userData The data to update
   * @returns True if the user was updated, false otherwise
   */
  async updateUser(
    id: string,
    userData: Partial<{
      unit_id: string;
      code: string;
      name: string;
      email: string;
      phone: string;
      dob: Date;
      gender: UserGender;
      username: string;
      password: string;
      face_id: string;
      member_role_id: string;
      tenant_id: string;
      current_tenant_id: string;
      affiliated_tenants: string[];
    }>,
  ): Promise<boolean> {
    // Check if the user exists
    const user = await this.findById(id);
    if (!user) {
      throw new Error(`User with ID '${id}' not found`);
    }

    // Validate email format if provided
    if (userData.email && !userData.email.includes('@')) {
      throw new Error('Invalid email format');
    }

    // Check for unique constraints
    if (userData.username && userData.username !== user.username) {
      const existingUsername = await this.usersRepository.findByUsername(userData.username);
      if (existingUsername && existingUsername.id !== id) {
        throw new Error(`Username '${userData.username}' is already taken`);
      }
    }

    if (userData.email && userData.email !== user.email) {
      const existingEmail = await this.usersRepository.findByEmail(userData.email);
      if (existingEmail && existingEmail.id !== id) {
        throw new Error(`Email '${userData.email}' is already registered`);
      }
    }

    if (userData.code && userData.code !== user.code) {
      const existingCode = await this.usersRepository.findByCode(userData.code);
      if (existingCode && existingCode.id !== id) {
        throw new Error(`User code '${userData.code}' is already in use`);
      }
    }

    // Update the user with proper gender typing
    const userDataTyped = {
      ...userData,
      gender: userData.gender ? (userData.gender as UserGender) : undefined,
    };
    return this.update(id, userDataTyped);
  }

  /**
   * Authenticate a user
   * @param username The username
   * @param password The password
   * @returns The authenticated user or null if authentication fails
   */
  async authenticate(username: string, password: string): Promise<UsersDocument | null> {
    // Find the user by username
    const user = await this.usersRepository.findByUsernameSelectPassword(username);

    const isValidPassword = (await user?.checkPassword(password)) || false;

    // If no user is found or the password is incorrect, return null
    if (!user || !isValidPassword) {
      return null;
    }

    // Return the user without the password field
    // Use the id property which is guaranteed to be a string
    return user;
  }

  /**
   * Find a user by username
   * @param username The username
   * @returns The user or null if not found
   */
  async findByUsername(username: string): Promise<UsersDocument | null> {
    return this.usersRepository.findByUsername(username);
  }

  /**
   * Find a user by email
   * @param email The email
   * @returns The user or null if not found
   */
  async findByEmail(email: string): Promise<UsersDocument | null> {
    return this.usersRepository.findByEmail(email);
  }

  /**
   * Find a user by code
   * @param code The user code
   * @returns The user or null if not found
   */
  async findByCode(code: string): Promise<UsersDocument | null> {
    return this.usersRepository.findByCode(code);
  }

  /**
   * Find users by unit ID
   * @param unitId The unit ID
   * @returns An array of users
   */
  async findByUnitId(unitId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByUnitId(unitId);
  }

  /**
   * Find users by unit ID with populated tenant and unit information
   * @param unitId The unit ID
   * @returns An array of users with populated data
   */
  async findByUnitIdWithPopulate(unitId: string): Promise<any[]> {
    return this.usersRepository.findByUnitIdWithPopulate(unitId);
  }

  /**
   * Find users by unit ID with populated tenant and unit information (paginated)
   * @param unitId The unit ID
   * @param pageSize Number of items per page
   * @param pageIndex Page index (0-based)
   * @returns Paginated result with users and pagination info
   */
  async findByUnitIdWithPopulatePaginated(
    unitId: string,
    pageSize: number,
    pageIndex: number,
  ): Promise<{
    users: any[];
    pageSize: number;
    pageIndex: number;
    totalPages: number;
    totalCount: number;
  }> {
    return this.usersRepository.findByUnitIdWithPopulatePaginated(unitId, pageSize, pageIndex);
  }

  /**
   * Find users by member role ID
   * @param memberRoleId The member role ID
   * @returns An array of users
   */
  async findByMemberRoleId(memberRoleId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByMemberRoleId(memberRoleId);
  }

  /**
   * Find users by member role ID filtered by tenant
   * @param memberRoleId The member role ID
   * @param tenantId The tenant ID to filter by
   * @returns An array of users
   */
  async findByMemberRoleIdAndTenant(
    memberRoleId: string,
    tenantId: string,
  ): Promise<UsersDocument[]> {
    return this.usersRepository.findByMemberRoleIdAndTenant(memberRoleId, tenantId);
  }

  /**
   * Find users by face ID
   * @param faceId The face ID
   * @returns An array of users
   */
  async findByFaceId(faceId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByFaceId(faceId);
  }

  /**
   * Find users by tenant ID
   * @param tenantId The tenant ID
   * @returns An array of users belonging to the tenant
   */
  async findByTenantId(tenantId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByTenantId(tenantId);
  }

  /**
   * Find users by tenant ID with populated tenant and unit information
   * @param tenantId The tenant ID
   * @returns An array of users with populated data
   */
  async findByTenantIdWithPopulate(tenantId: string): Promise<any[]> {
    return this.usersRepository.findByTenantIdWithPopulate(tenantId);
  }

  /**
   * Find users by tenant ID with populated tenant and unit information (paginated)
   * @param tenantId The tenant ID
   * @param pageSize Number of items per page
   * @param pageIndex Page index (0-based)
   * @returns Paginated result with users and pagination info
   */
  async findByTenantIdWithPopulatePaginated(
    tenantId: string,
    pageSize: number,
    pageIndex: number,
  ): Promise<{
    users: any[];
    pageSize: number;
    pageIndex: number;
    totalPages: number;
    totalCount: number;
  }> {
    return this.usersRepository.findByTenantIdWithPopulatePaginated(tenantId, pageSize, pageIndex);
  }

  /**
   * Generate a unique member code in format E00001, E00002, E00003, etc.
   * @returns A unique member code
   */
  async generateMemberCode(): Promise<string> {
    return this.usersRepository.generateMemberCode();
  }

  /**
   * Validate that a unit belongs to a specific tenant
   * @param unitId The unit ID to validate
   * @param tenantId The tenant ID to check against
   * @returns True if the unit belongs to the tenant, false otherwise
   */
  async validateUnitBelongsToTenant(unitId: string, tenantId: string): Promise<boolean> {
    try {
      const UnitModel = (await import('../database/entities/UnitModel')).default;
      const unit = await UnitModel.findById(unitId).lean().exec();

      if (!unit) {
        return false;
      }

      // Check if the unit's organization_id matches the tenant ID
      return unit.organization_id === tenantId;
    } catch (error) {
      console.error('Error validating unit belongs to tenant:', error);
      return false;
    }
  }

  /**
   * Upload user avatar
   * @param userId The user ID
   * @param fileName The file name
   * @param fileData The base64 encoded file data
   * @returns The file ID
   */
  async uploadUserAvatar(userId: string, fileName: string, fileData: string): Promise<string> {
    // Check if user exists
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`User with ID '${userId}' not found`);
    }

    // Convert base64 to buffer
    const buffer = Buffer.from(fileData, 'base64');

    // Get or create avatar bucket
    const bucketName = 'user-avatars';
    let bucket = await this.storageService.getBucketByName(bucketName);

    if (!bucket) {
      // Create bucket if it doesn't exist
      bucket = await this.storageService.createBucket(
        {
          name: bucketName,
          description: 'User avatar storage',
          is_public: false,
          region: 'us-east-1',
        },
        userId,
      );
    }

    // Upload file using FilesService
    const fileRecord = await this.filesService.uploadFile(
      bucket.id,
      fileName,
      buffer,
      userId,
      {
        type: 'avatar',
        user_id: userId,
      },
      ['avatar', 'user'],
      false, // Not public
    );

    // Delete old avatar file if exists
    if (user.avatar_id) {
      try {
        await this.filesService.deleteFile(user.avatar_id);
      } catch (error) {
        // Log error but don't fail the upload
        console.warn(`Failed to delete old avatar file: ${user.avatar_id}`, error);
      }
    }

    // Update user with new avatar file ID
    await this.update(userId, { avatar_id: fileRecord.id });

    return fileRecord.id;
  }

  /**
   * Update status for multiple users
   * @param userIds Array of user IDs to update
   * @param status The new status
   * @param updatedBy The ID of the user performing the update (for audit purposes)
   * @returns Result with updated count and failed updates
   */
  async updateUsersStatus(
    userIds: string[],
    status: string,
    updatedBy: string,
  ): Promise<{
    updatedCount: number;
    failedUpdates: Array<{ userId: string; error: string }>;
    totalRequested: number;
  }> {
    const failedUpdates: Array<{ userId: string; error: string }> = [];
    let updatedCount = 0;

    // Log the update operation for audit purposes
    console.log(`User ${updatedBy} updating status to ${status} for ${userIds.length} users`);

    // Update each user individually to handle errors gracefully
    for (const userId of userIds) {
      try {
        // Check if user exists
        const user = await this.findById(userId);
        if (!user) {
          failedUpdates.push({
            userId,
            error: `User with ID '${userId}' not found`,
          });
          continue;
        }

        // Update user's status
        const updateResult = await this.update(userId, {
          status: status as any, // Cast to match the expected type
        });

        if (updateResult) {
          updatedCount++;
        } else {
          failedUpdates.push({
            userId,
            error: 'Failed to update user status',
          });
        }
      } catch (error) {
        failedUpdates.push({
          userId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return {
      updatedCount,
      failedUpdates,
      totalRequested: userIds.length,
    };
  }

  /**
   * Switch user's current tenant
   * @param userId The user ID
   * @param tenantId The new current tenant ID
   * @returns True if switched successfully
   */
  async switchCurrentTenant(userId: string, tenantId: string): Promise<boolean> {
    // Check if user exists
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`User with ID '${userId}' not found`);
    }

    // Check if user has access to the tenant
    if (!user.affiliated_tenants.includes(tenantId)) {
      throw new Error(`User does not have access to tenant '${tenantId}'`);
    }

    return this.usersRepository.updateCurrentTenant(userId, tenantId);
  }

  /**
   * Add tenant to user's affiliated tenants
   * @param userId The user ID
   * @param tenantId The tenant ID to add
   * @returns True if added successfully
   */
  async addUserToTenant(userId: string, tenantId: string): Promise<boolean> {
    // Check if user exists
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`User with ID '${userId}' not found`);
    }

    return this.usersRepository.addAffiliatedTenant(userId, tenantId);
  }

  /**
   * Remove tenant from user's affiliated tenants
   * @param userId The user ID
   * @param tenantId The tenant ID to remove
   * @returns True if removed successfully
   */
  async removeUserFromTenant(userId: string, tenantId: string): Promise<boolean> {
    // Check if user exists
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`User with ID '${userId}' not found`);
    }

    // Don't allow removing the tenant that created the user
    if (user.tenant_id === tenantId) {
      throw new Error('Cannot remove user from their creator tenant');
    }

    return this.usersRepository.removeAffiliatedTenant(userId, tenantId);
  }

  /**
   * Find users by current tenant ID
   * @param tenantId The current tenant ID
   * @returns Array of users with the specified current tenant
   */
  async findByCurrentTenantId(tenantId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByCurrentTenantId(tenantId);
  }

  /**
   * Find users who have access to a specific tenant
   * @param tenantId The tenant ID
   * @returns Array of users who have access to the tenant
   */
  async findByAffiliatedTenant(tenantId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByAffiliatedTenant(tenantId);
  }

  /**
   * Find users created by a specific tenant
   * @param tenantId The tenant ID that created the users
   * @returns Array of users created by the tenant
   */
  async findByCreatorTenant(tenantId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByCreatorTenant(tenantId);
  }

  /**
   * Find users with tenant-based filtering
   * This implements the main filtering logic for tenant-based data access
   * @param currentUserId The current user's ID
   * @param currentTenantId The current tenant ID
   * @returns Array of users that the current user can see
   */
  async findWithTenantFiltering(
    currentUserId: string,
    currentTenantId: string
  ): Promise<UsersDocument[]> {
    return this.usersRepository.findWithTenantFiltering(currentUserId, currentTenantId);
  }
}

export default UsersService;
