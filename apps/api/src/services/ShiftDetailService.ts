import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { ShiftDetailDocument } from '@/database/entities/ShiftDetailModel';
import ShiftDetailRepository from '@/repositories/ShiftDetailRepository';

/**
 * Service for managing shift details
 * Extends the BaseModel with ShiftDetailDocument type
 */
@Injectable()
class ShiftDetailService extends BaseModel<ShiftDetailDocument> {
  protected repository: ShiftDetailRepository;

  /**
   * Create a new ShiftDetailService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(ShiftDetailRepository)
    repository: ShiftDetailRepository,
  ) {
    super(repository);
    this.repository = repository;
  }

  /**
   * Create a new shift detail
   * @param shiftDetailData The shift detail data
   * @returns The newly created shift detail
   */
  async createShiftDetail(shiftDetailData: Partial<ShiftDetailDocument>): Promise<ShiftDetailDocument> {
    // Basic validation can be added here if needed

    // Create the new shift detail
    return this.create(shiftDetailData);
  }

  /**
   * Update a shift detail
   * @param id The shift detail ID
   * @param shiftDetailData The data to update
   * @returns True if the shift detail was updated, false otherwise
   */
  async updateShiftDetail(
    id: string,
    shiftDetailData: Partial<ShiftDetailDocument>,
  ): Promise<boolean> {
    // Check if the shift detail exists
    const shiftDetail = await this.findById(id);
    if (!shiftDetail) {
      throw new Error(`Shift detail with ID '${id}' not found`);
    }

    // Basic validation can be added here if needed

    // Update the shift detail
    return this.update(id, shiftDetailData);
  }



  /**
   * Find shift details by tenant ID
   * @param tenantId The tenant ID
   * @returns An array of shift details
   */
  async findByTenantId(tenantId: string): Promise<ShiftDetailDocument[]> {
    return (this.repository as ShiftDetailRepository).findByTenantId(tenantId);
  }

  /**
   * Create shift detail with auto-generated code
   * @param shiftDetailData The shift detail data
   * @returns The newly created shift detail
   */
  async createShiftDetailWithAutoCode(shiftDetailData: Partial<ShiftDetailDocument>): Promise<ShiftDetailDocument> {
    if (!shiftDetailData.tenant_id) {
      throw new Error('tenant_id is required');
    }

    return this.create(shiftDetailData);
  }




}

export default ShiftDetailService;
