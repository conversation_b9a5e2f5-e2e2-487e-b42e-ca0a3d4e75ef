import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { CreateShiftRequest } from '@/hooks/useShift';

interface CreateShiftPopupProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CreateShiftRequest) => void;
  loading?: boolean;
}

interface FormData {
  name: string;
  type: TabsCreateShift;
  work_coefficient: number;

  // Fixed shift fields
  start_time: string;
  check_in_from: string;
  late_threshold_enabled: boolean;
  late_threshold_minutes: number;
  half_day_threshold_enabled: boolean;
  half_day_threshold_minutes: number;
  break_enabled: boolean;
  break_start: string;
  break_end: string;
  end_time: string;
  check_out_until: string;
  early_leave_threshold_enabled: boolean;
  early_leave_threshold_minutes: number;
  half_day_end_threshold_enabled: boolean;
  half_day_end_threshold_minutes: number;
  overnight_enabled: boolean;

  // Flexible shift fields
  total_hours: number;
  total_minutes: number;
  check_in_required: boolean;
  latest_check_in: string;
  flex_late_threshold_enabled: boolean;
  flex_late_threshold_minutes: number;
  flex_no_work_threshold_enabled: boolean;
  flex_no_work_threshold_minutes: number;
}

interface FormErrors {
  [key: string]: string;
}

enum TabsCreateShift {
  Fixed = "Fixed",
  Flexible = "Flexible"
}

// Simple TimePicker component
const TimePicker = ({ value, onChange, className }: { value: string; onChange: (value: string) => void; className?: string }) => {
  return (
    <Input
      type="time"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={className}
    />
  );
};

export const CreateShiftPopup = ({ open, onClose, onSubmit, loading }: CreateShiftPopupProps) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    type: TabsCreateShift.Fixed,
    work_coefficient: 1,

    // Fixed shift defaults
    start_time: '08:00',
    check_in_from: '07:30',
    late_threshold_enabled: false,
    late_threshold_minutes: 15,
    half_day_threshold_enabled: false,
    half_day_threshold_minutes: 30,
    break_enabled: false,
    break_start: '12:00',
    break_end: '13:00',
    end_time: '17:00',
    check_out_until: '18:00',
    early_leave_threshold_enabled: false,
    early_leave_threshold_minutes: 15,
    half_day_end_threshold_enabled: false,
    half_day_end_threshold_minutes: 30,
    overnight_enabled: false,

    // Flexible shift defaults
    total_hours: 8,
    total_minutes: 0,
    check_in_required: false,
    latest_check_in: '09:00',
    flex_late_threshold_enabled: false,
    flex_late_threshold_minutes: 15,
    flex_no_work_threshold_enabled: false,
    flex_no_work_threshold_minutes: 60,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Calculate total working time for fixed shift
  const calculateWorkingTime = () => {
    if (formData.type !== 'Fixed') return { hours: 0, minutes: 0 };

    const startTime = new Date(`2000-01-01 ${formData.start_time}`);
    const endTime = new Date(`2000-01-01 ${formData.end_time}`);

    let totalMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);

    // Subtract break time if enabled
    if (formData.break_enabled) {
      const breakStart = new Date(`2000-01-01 ${formData.break_start}`);
      const breakEnd = new Date(`2000-01-01 ${formData.break_end}`);
      const breakMinutes = (breakEnd.getTime() - breakStart.getTime()) / (1000 * 60);
      totalMinutes -= breakMinutes;
    }

    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    return { hours, minutes };
  };

  // Calculate break time
  const calculateBreakTime = () => {
    if (!formData.break_enabled) return { hours: 0, minutes: 0 };

    const breakStart = new Date(`2000-01-01 ${formData.break_start}`);
    const breakEnd = new Date(`2000-01-01 ${formData.break_end}`);
    const totalMinutes = (breakEnd.getTime() - breakStart.getTime()) / (1000 * 60);

    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    return { hours, minutes };
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Common validations
    if (!formData.name.trim()) {
      newErrors.name = 'Vui lòng nhập thông tin tên ca làm việc';
    }

    if (!formData.work_coefficient || formData.work_coefficient <= 0) {
      newErrors.work_coefficient = 'Vui lòng nhập thông tin hệ số công';
    }

    if (formData.type === 'Fixed') {
      // Fixed shift validations
      if (!formData.start_time) {
        newErrors.start_time = 'Vui lòng chọn giờ bắt đầu ca';
      }

      if (!formData.check_in_from) {
        newErrors.check_in_from = 'Vui lòng chọn giờ cho phép chấm công vào';
      } else if (formData.check_in_from >= formData.start_time) {
        newErrors.check_in_from = 'Giờ cho phép chấm công phải trước giờ bắt đầu ca';
      }

      if (!formData.end_time) {
        newErrors.end_time = 'Vui lòng chọn thời gian';
      }

      if (!formData.check_out_until) {
        newErrors.check_out_until = 'Vui lòng chọn thời gian';
      } else if (formData.check_out_until <= formData.end_time) {
        newErrors.check_out_until = 'Giờ chấm công ra phải lớn hơn giờ kết thúc ca';
      }

      if (formData.break_enabled) {
        if (!formData.break_start || !formData.break_end) {
          newErrors.break_time = 'Vui lòng chọn thời gian nghỉ giữa giờ';
        } else if (formData.break_end <= formData.break_start) {
          newErrors.break_time = 'Vui lòng chọn thời gian kết thúc lớn hơn bắt đầu';
        }
      }
    } else {
      // Flexible shift validations
      if (!formData.total_hours && !formData.total_minutes) {
        newErrors.total_time = 'Nhập thời gian làm việc';
      }

      if (formData.check_in_required && !formData.latest_check_in) {
        newErrors.latest_check_in = 'Nhập thời gian chấm công';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    const shiftData = {
      name: formData.name,
      type: formData.type,
      work_coefficient: formData.work_coefficient,
      tenant_id: 'default-tenant', // You should get this from context or props
    };

    const shiftDetailData = {
      name: formData.name,
      work_shift_type: 'Administrative' as const,
      entity_type: 'All' as const,
      assignment_type: 'Weekly' as const,
      dateRange_type: 'InfinityDay' as const,
      start_date: new Date(),
      end_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      status: 'active' as const,
      targets: [],
      excluded_targets: [],
    };

    onSubmit({ shiftData, shiftDetailData });
  };

  const handleClose = () => {
    setFormData({
      name: '',
      type: TabsCreateShift.Fixed,
      work_coefficient: 1,
      start_time: '08:00',
      check_in_from: '07:30',
      late_threshold_enabled: false,
      late_threshold_minutes: 15,
      half_day_threshold_enabled: false,
      half_day_threshold_minutes: 30,
      break_enabled: false,
      break_start: '12:00',
      break_end: '13:00',
      end_time: '17:00',
      check_out_until: '18:00',
      early_leave_threshold_enabled: false,
      early_leave_threshold_minutes: 15,
      half_day_end_threshold_enabled: false,
      half_day_end_threshold_minutes: 30,
      overnight_enabled: false,
      total_hours: 8,
      total_minutes: 0,
      check_in_required: false,
      latest_check_in: '09:00',
      flex_late_threshold_enabled: false,
      flex_late_threshold_minutes: 15,
      flex_no_work_threshold_enabled: false,
      flex_no_work_threshold_minutes: 60,
    });
    setErrors({});
    onClose();
  };

  const workingTime = calculateWorkingTime();
  const breakTime = calculateBreakTime();

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-max max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Thêm ca làm việc</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Thông tin cơ bản */}
          <Card>
            <CardHeader>
              <CardTitle>Thông tin cơ bản</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Tên ca làm việc */}
              <div className="space-y-2">
                <Label htmlFor="name">Tên ca làm việc *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Nhập tên ca làm việc"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
              </div>

              {/* Hệ số công */}
              <div className="space-y-2">
                <Label htmlFor="work_coefficient">Hệ số công *</Label>
                <Input
                  id="work_coefficient"
                  type="number"
                  step="0.1"
                  value={formData.work_coefficient}
                  onChange={(e) => setFormData({ ...formData, work_coefficient: parseFloat(e.target.value) || 0 })}
                  placeholder="Nhập hệ số công"
                  className={errors.work_coefficient ? 'border-red-500' : ''}
                />
                {errors.work_coefficient && <p className="text-sm text-red-500">{errors.work_coefficient}</p>}
              </div>
            </CardContent>
          </Card>

          {/* Cấu hình ca làm việc */}
          <Card>
            <CardHeader>
              <CardTitle>Cấu hình ca làm việc</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={formData.type} onValueChange={(value: string) => setFormData({ ...formData, type: value as TabsCreateShift })}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="Fixed">Giờ làm việc cố định</TabsTrigger>
                  <TabsTrigger value="Flexible">Giờ làm việc linh hoạt</TabsTrigger>
                </TabsList>

                <TabsContent value="Fixed" className="space-y-4 mt-4">
                  {/* Working time header */}
                  <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                    <div className="text-sm font-medium">
                      Tổng thời gian làm việc (Không tính nghỉ giữa giờ): {workingTime.hours} giờ {workingTime.minutes} phút
                    </div>
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="overnight" className="text-sm">Làm việc qua ngày</Label>
                      <Switch
                        id="overnight"
                        checked={formData.overnight_enabled}
                        onCheckedChange={(checked) => setFormData({ ...formData, overnight_enabled: checked })}
                      />
                    </div>
                  </div>

                  {/* Start time and check-in time */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start_time">Bắt đầu ca *</Label>
                      <TimePicker
                        value={formData.start_time}
                        onChange={(value) => setFormData({ ...formData, start_time: value })}
                        className={errors.start_time ? 'border-red-500' : ''}
                      />
                      {errors.start_time && <p className="text-sm text-red-500">{errors.start_time}</p>}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="check_in_from">Cho phép chấm công vào từ *</Label>
                      <TimePicker
                        value={formData.check_in_from}
                        onChange={(value) => setFormData({ ...formData, check_in_from: value })}
                        className={errors.check_in_from ? 'border-red-500' : ''}
                      />
                      {errors.check_in_from && <p className="text-sm text-red-500">{errors.check_in_from}</p>}
                    </div>
                  </div>

                  <Separator />

                  {/* Late threshold */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="late_threshold"
                      checked={formData.late_threshold_enabled}
                      onCheckedChange={(checked) => setFormData({ ...formData, late_threshold_enabled: !!checked })}
                    />
                    <Label htmlFor="late_threshold">Chấm công vào sau</Label>
                    <Input
                      type="number"
                      value={formData.late_threshold_minutes}
                      onChange={(e) => setFormData({ ...formData, late_threshold_minutes: parseInt(e.target.value) || 0 })}
                      className="w-20"
                      disabled={!formData.late_threshold_enabled}
                    />
                    <span>phút thì tính là đến muộn</span>
                  </div>

                  {/* Half day threshold */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="half_day_threshold"
                      checked={formData.half_day_threshold_enabled}
                      onCheckedChange={(checked) => setFormData({ ...formData, half_day_threshold_enabled: !!checked })}
                    />
                    <Label htmlFor="half_day_threshold">Chấm công vào sau</Label>
                    <Input
                      type="number"
                      value={formData.half_day_threshold_minutes}
                      onChange={(e) => setFormData({ ...formData, half_day_threshold_minutes: parseInt(e.target.value) || 0 })}
                      className="w-20"
                      disabled={!formData.half_day_threshold_enabled}
                    />
                    <span>phút thì không ghi nhận công nửa ca đầu</span>
                  </div>

                  <Separator />

                  {/* Break time */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="break_enabled"
                        checked={formData.break_enabled}
                        onCheckedChange={(checked) => setFormData({ ...formData, break_enabled: !!checked })}
                      />
                      <Label htmlFor="break_enabled">Nghỉ giữa giờ</Label>
                    </div>

                    {formData.break_enabled && (
                      <Card className="ml-6">
                        <CardContent className="pt-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="break_start">Thời gian bắt đầu</Label>
                              <TimePicker
                                value={formData.break_start}
                                onChange={(value) => setFormData({ ...formData, break_start: value })}
                                className={errors.break_time ? 'border-red-500' : ''}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="break_end">Thời gian kết thúc</Label>
                              <TimePicker
                                value={formData.break_end}
                                onChange={(value) => setFormData({ ...formData, break_end: value })}
                                className={errors.break_time ? 'border-red-500' : ''}
                              />
                            </div>
                          </div>
                          <div className="mt-2">
                            <p className="text-sm text-muted-foreground">
                              Tổng thời gian: {breakTime.hours} giờ {breakTime.minutes} phút
                            </p>
                          </div>
                          {errors.break_time && <p className="text-sm text-red-500 mt-2">{errors.break_time}</p>}
                        </CardContent>
                      </Card>
                    )}
                  </div>

                  <Separator />

                  {/* End time and check-out time */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="end_time">Kết thúc ca *</Label>
                      <TimePicker
                        value={formData.end_time}
                        onChange={(value) => setFormData({ ...formData, end_time: value })}
                        className={errors.end_time ? 'border-red-500' : ''}
                      />
                      {errors.end_time && <p className="text-sm text-red-500">{errors.end_time}</p>}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="check_out_until">Cho phép chấm công ra đến *</Label>
                      <TimePicker
                        value={formData.check_out_until}
                        onChange={(value) => setFormData({ ...formData, check_out_until: value })}
                        className={errors.check_out_until ? 'border-red-500' : ''}
                      />
                      {errors.check_out_until && <p className="text-sm text-red-500">{errors.check_out_until}</p>}
                    </div>
                  </div>

                  <Separator />

                  {/* Early leave threshold */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="early_leave_threshold"
                      checked={formData.early_leave_threshold_enabled}
                      onCheckedChange={(checked) => setFormData({ ...formData, early_leave_threshold_enabled: !!checked })}
                    />
                    <Label htmlFor="early_leave_threshold">Chấm công ra trước</Label>
                    <Input
                      type="number"
                      value={formData.early_leave_threshold_minutes}
                      onChange={(e) => setFormData({ ...formData, early_leave_threshold_minutes: parseInt(e.target.value) || 0 })}
                      className="w-20"
                      disabled={!formData.early_leave_threshold_enabled}
                    />
                    <span>phút thì tính là về sớm</span>
                  </div>

                  {/* Half day end threshold */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="half_day_end_threshold"
                      checked={formData.half_day_end_threshold_enabled}
                      onCheckedChange={(checked) => setFormData({ ...formData, half_day_end_threshold_enabled: !!checked })}
                    />
                    <Label htmlFor="half_day_end_threshold">Chấm công ra trước</Label>
                    <Input
                      type="number"
                      value={formData.half_day_end_threshold_minutes}
                      onChange={(e) => setFormData({ ...formData, half_day_end_threshold_minutes: parseInt(e.target.value) || 0 })}
                      className="w-20"
                      disabled={!formData.half_day_end_threshold_enabled}
                    />
                    <span>phút thì không ghi nhận công nửa ca sau</span>
                  </div>
                </TabsContent>

                <TabsContent value="Flexible" className="space-y-4 mt-4">
                  {/* Total working time */}
                  <div className="space-y-2">
                    <Label>Tổng số giờ làm việc *</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        value={formData.total_hours}
                        onChange={(e) => setFormData({ ...formData, total_hours: parseInt(e.target.value) || 0 })}
                        className="w-20"
                        placeholder="Giờ"
                      />
                      <span>giờ</span>
                      <Input
                        type="number"
                        value={formData.total_minutes}
                        onChange={(e) => setFormData({ ...formData, total_minutes: parseInt(e.target.value) || 0 })}
                        className="w-20"
                        placeholder="Phút"
                      />
                      <span>phút</span>
                    </div>
                    {errors.total_time && <p className="text-sm text-red-500">{errors.total_time}</p>}
                  </div>

                  <Separator />

                  {/* Check-in required */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="check_in_required"
                        checked={formData.check_in_required}
                        onCheckedChange={(checked) => setFormData({ ...formData, check_in_required: !!checked })}
                      />
                      <Label htmlFor="check_in_required">Yêu cầu chấm công</Label>
                    </div>

                    {formData.check_in_required && (
                      <Card className="ml-6">
                        <CardContent className="pt-4 space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="latest_check_in">Chấm công muộn nhất trước *</Label>
                            <TimePicker
                              value={formData.latest_check_in}
                              onChange={(value) => setFormData({ ...formData, latest_check_in: value })}
                              className={errors.latest_check_in ? 'border-red-500' : ''}
                            />
                            {errors.latest_check_in && <p className="text-sm text-red-500">{errors.latest_check_in}</p>}
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="flex_late_threshold"
                              checked={formData.flex_late_threshold_enabled}
                              onCheckedChange={(checked) => setFormData({ ...formData, flex_late_threshold_enabled: !!checked })}
                            />
                            <Label htmlFor="flex_late_threshold">Chấm công vào sau</Label>
                            <Input
                              type="number"
                              value={formData.flex_late_threshold_minutes}
                              onChange={(e) => setFormData({ ...formData, flex_late_threshold_minutes: parseInt(e.target.value) || 0 })}
                              className="w-20"
                              disabled={!formData.flex_late_threshold_enabled}
                            />
                            <span>phút thì tính là đến muộn</span>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="flex_no_work_threshold"
                              checked={formData.flex_no_work_threshold_enabled}
                              onCheckedChange={(checked) => setFormData({ ...formData, flex_no_work_threshold_enabled: !!checked })}
                            />
                            <Label htmlFor="flex_no_work_threshold">Chấm công vào sau</Label>
                            <Input
                              type="number"
                              value={formData.flex_no_work_threshold_minutes}
                              onChange={(e) => setFormData({ ...formData, flex_no_work_threshold_minutes: parseInt(e.target.value) || 0 })}
                              className="w-20"
                              disabled={!formData.flex_no_work_threshold_enabled}
                            />
                            <span>phút thì không ghi nhận công cho toàn bộ ca làm việc</span>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

        </div>

        <Separator />

        {/* Footer */}
        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Hủy bỏ
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Đang tạo...' : 'Thêm ca làm việc'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
