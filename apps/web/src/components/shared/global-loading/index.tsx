import { useLoading } from '@/contexts/loading-context'
import { Loader2 } from 'lucide-react'

export function GlobalLoading() {
  const { isVisible, loadingMessage, loadingCount } = useLoading()

  if (!isVisible) {
    return null
  }

  return (
    <>
      {/* Backdrop overlay to block user interaction */}
      <div className="fixed inset-0 bg-gray-500/20 backdrop-blur-[1px] z-[9998] animate-in fade-in duration-200" />

      {/* Loading Message Popup - Compact with smooth animation */}
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] animate-in slide-in-from-top-2 fade-in duration-300">
        <div className="bg-white rounded-md shadow-lg px-3 py-2 flex items-center gap-2 border border-gray-200 transition-all duration-200">
          {/* Spinning Icon */}
          <Loader2 className="h-4 w-4 animate-spin text-[#008fd3]" />

          {/* Loading Message */}
          <span className="text-xs font-medium text-[#1f2329] whitespace-nowrap transition-all duration-200">
            {loadingMessage}
          </span>

          {/* Loading count indicator (optional, for debugging) */}
          {loadingCount > 1 && (
            <span className="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded-full">
              {loadingCount}
            </span>
          )}
        </div>
      </div>
    </>
  )
}

// Alternative with Ant Design style - Compact Top Version
export function AntdStyleLoading() {
  const { isLoading, loadingMessage } = useLoading()

  if (!isLoading) {
    return null
  }

  return (
    <>
      {/* Backdrop overlay to block user interaction */}
      <div className="fixed inset-0 bg-gray-500/20 backdrop-blur-[1px] z-[9998]" />

      {/* Loading Message Popup - Ant Design Style Compact */}
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] animate-in slide-in-from-top-2 duration-300">
        <div className="bg-white rounded-md shadow-lg px-3 py-2 flex items-center gap-2 border border-gray-200">
          {/* Custom Spinning Dots - Smaller */}
          <div className="flex gap-1">
            <div className="w-1.5 h-1.5 bg-[#008fd3] rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-1.5 h-1.5 bg-[#008fd3] rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-1.5 h-1.5 bg-[#008fd3] rounded-full animate-bounce"></div>
          </div>

          {/* Loading Message */}
          <span className="text-xs text-[#1f2329] whitespace-nowrap">
            {loadingMessage}
          </span>
        </div>
      </div>
    </>
  )
}

// Spinner component for reuse
export function LoadingSpinner({ size = 'default' }: { size?: 'small' | 'default' | 'large' }) {
  const sizeClasses = {
    small: 'h-4 w-4',
    default: 'h-5 w-5',
    large: 'h-6 w-6'
  }

  return (
    <Loader2 className={`animate-spin text-[#008fd3] ${sizeClasses[size]}`} />
  )
}

// Loading dots animation
export function LoadingDots({ size = 'default' }: { size?: 'small' | 'default' | 'large' }) {
  const sizeClasses = {
    small: 'w-1 h-1',
    default: 'w-2 h-2',
    large: 'w-3 h-3'
  }

  return (
    <div className="flex gap-1">
      <div className={`bg-[#008fd3] rounded-full animate-bounce [animation-delay:-0.3s] ${sizeClasses[size]}`}></div>
      <div className={`bg-[#008fd3] rounded-full animate-bounce [animation-delay:-0.15s] ${sizeClasses[size]}`}></div>
      <div className={`bg-[#008fd3] rounded-full animate-bounce ${sizeClasses[size]}`}></div>
    </div>
  )
}
