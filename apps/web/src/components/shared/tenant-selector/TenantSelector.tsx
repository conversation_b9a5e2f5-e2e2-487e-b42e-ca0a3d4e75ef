import { PlusIcon } from '@/components/shared/icons'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useSelectedTenant } from '@/hooks/use-selected-tenant'
import { cn } from '@/utils/cn'
import React, { useMemo, useState } from 'react'
import CreateTenantModal from './CreateTenantModal'

export interface Tenant {
  _id: string
  id: string
  name: string
  address?: string
  created_by?: string
  createdAt?: string
  updatedAt?: string
}

export interface TenantSelectorProps {
  tenants: Tenant[]
  placeholder?: string
  className?: string
  disabled?: boolean
  onTenantChange?: (tenant: Tenant | null) => void
  onAddTenant?: () => void
  onTenantCreated?: (newTenant: Tenant) => void
}

const TenantSelector: React.FC<TenantSelectorProps> = ({
  tenants,
  placeholder = 'Chọn tenant',
  className,
  disabled = false,
  onTenantChange,
  onAddTenant,
  onTenantCreated,
}) => {
  const { selectedTenant, setTenant, isLoading } = useSelectedTenant()
  const [searchKeyword, setSearchKeyword] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [localTenants, setLocalTenants] = useState<Tenant[]>(tenants)

  // Update local tenants when props change
  useMemo(() => {
    setLocalTenants(tenants)
  }, [tenants])

  // Filter tenants based on search keyword
  const filteredTenants = useMemo(() => {
    if (!searchKeyword.trim()) return localTenants
    return localTenants.filter((tenant) =>
      tenant.name.toLowerCase().includes(searchKeyword.toLowerCase()),
    )
  }, [localTenants, searchKeyword])

  const handleValueChange = (value: string) => {
    const tenant = localTenants.find((t) => t.id === value)
    if (tenant) {
      setTenant(tenant)
      onTenantChange?.(tenant)
    }
  }

  // Function to handle when a new tenant is created from the modal
  const handleTenantCreated = (newTenant: Tenant) => {
    console.log('New tenant created:', newTenant)

    // Note: Tenant switching is now handled automatically by CreateTenantModal
    // via handleCreateTenantWithAutoSwitch, so we don't need to manually set tenant here

    // Call the parent callback if provided
    onTenantCreated?.(newTenant)

    // Close the modal
    setIsCreateModalOpen(false)
  }

  // Generate avatar initials like in the Ant Design version
  const getAvatarInitials = (name: string) => {
    const words = name?.split(' ').filter(Boolean) || []
    if (words.length === 1) {
      return words[0]?.slice(0, 2).toUpperCase() || ''
    } else if (words.length > 1) {
      return (
        (words[0]?.[0] || '') + (words[words.length - 1]?.[0] || '')
      ).toUpperCase()
    }
    return ''
  }

  if (isLoading) {
    return (
      <div className={cn('w-full', className)}>
        <div className="h-10 bg-gray-100 animate-pulse rounded-md" />
      </div>
    )
  }

  return (
    <>
      <div className={cn('w-full', className)}>
        <Select
          value={selectedTenant?.id || ''}
          onValueChange={handleValueChange}
          disabled={disabled || tenants.length === 0}
        >
          <SelectTrigger className="w-full h-[42px]">
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent className="w-[300px] max-h-[300px] p-1.5">
            {/* Search input */}
            <div className="mb-1.5">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-4 w-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Theo tên tổ chức"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Tenant list */}
            <div className="pb-2 max-h-[150px] overflow-y-auto">
              {filteredTenants.length > 0 ? (
                filteredTenants
                  .filter((tenant) => tenant.name !== 'Root')
                  .map((tenant) => (
                    <SelectItem
                      key={tenant.id}
                      value={tenant.id}
                      className="cursor-pointer"
                    >
                      <div className="flex items-center w-full">
                        <div className="bg-[#008FD31A] text-[#008FD3] flex items-center justify-center rounded-full px-1.5 py-1.5 mr-3 min-w-[32px] max-h-[32px]">
                          <span className="text-[12px] font-bold">
                            {getAvatarInitials(tenant.name)}
                          </span>
                        </div>
                        <p className="font-semibold">{tenant.name}</p>
                      </div>
                    </SelectItem>
                  ))
              ) : (
                <div className="text-center py-4">
                  <div className="flex flex-col items-center">
                    <svg
                      className="h-10 w-10 text-gray-400 mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"
                      />
                    </svg>
                    <p className="text-gray-500 text-sm">
                      Không tìm thấy tổ chức
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Add tenant button */}
            {onAddTenant && (
              <div className="pt-1 px-1">
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="flex items-center justify-center gap-2 w-full h-8 py-2 px-3 rounded-md bg-white border border-[#d0d3d6] hover:bg-[#008FD3]/5 hover:border-[#008FD3] transition-all duration-200 group cursor-pointer"
                >
                  <PlusIcon className="w-3.5 h-3.5" fillColor="#1F2329" />
                  <span className="text-sm font-medium text-[#1f2329] group-hover:text-[#008FD3]">
                    Thêm tổ chức
                  </span>
                </button>
              </div>
            )}
          </SelectContent>
        </Select>
      </div>

      {/* Create Tenant Modal */}
      <CreateTenantModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onConfirm={handleTenantCreated}
      />
    </>
  )
}

export default TenantSelector
