import {
  FormDialog,
  FormDialogActions,
  FormDialogButton,
  FormDialogField,
} from '@/components/shared/form-dialog'
import { Input } from '@/components/ui/input'
import { useTenantSelection } from '@/hooks/use-tenant-actions'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

export interface Tenant {
  _id: string
  id: string
  name: string
  address?: string
  created_by?: string
  createdAt?: string
  updatedAt?: string
}

interface CreateTenantModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (newTenant: Tenant) => void
}

const createTenantSchema = z.object({
  name: z.string().min(1, 'Tên tổ chức không được để trống').trim(),
})

type CreateTenantFormData = z.infer<typeof createTenantSchema>

function CreateTenantModal({
  open,
  onOpenChange,
  onConfirm,
}: CreateTenantModalProps) {
  const { handleCreateTenantWithAutoSwitch } = useTenantSelection()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<CreateTenantFormData>({
    resolver: zodResolver(createTenantSchema),
    defaultValues: {
      name: '',
    },
  })

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = form

  const onSubmit = async (data: CreateTenantFormData) => {
    try {
      setIsSubmitting(true)
      await handleCreateTenantWithAutoSwitch({ name: data.name.trim() })

      // Call onConfirm to notify parent component (for any additional logic)
      // Note: The actual tenant switching is handled by handleCreateTenantWithAutoSwitch
      onConfirm({ name: data.name.trim() } as Tenant)
      handleCancel()
    } catch (error) {
      // Error handling is already done by handleCreateTenantWithAutoSwitch
      console.error('Error creating tenant:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    reset()
    onOpenChange(false)
  }

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Thêm mới tổ chức"
      className="!max-w-[500px] !w-[500px]"
      footer={
        <FormDialogActions>
          <FormDialogButton variant="secondary" onClick={handleCancel}>
            Hủy bỏ
          </FormDialogButton>
          <FormDialogButton
            variant="primary"
            onClick={handleSubmit(onSubmit)}
            disabled={!isValid || isSubmitting}
          >
            {isSubmitting ? 'Đang thêm...' : 'Thêm tổ chức'}
          </FormDialogButton>
        </FormDialogActions>
      }
    >
      <div className="flex flex-col gap-4 w-full">
        <FormDialogField label="Tên tổ chức" required={true}>
          <Input
            type="text"
            {...register('name')}
            placeholder="Nhập tên tổ chức"
            className={`w-full ${errors.name ? 'border-red-500' : ''}`}
          />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
          )}
        </FormDialogField>
      </div>
    </FormDialog>
  )
}

export default CreateTenantModal
