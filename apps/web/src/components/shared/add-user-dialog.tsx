import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { Eye, EyeOff } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import * as z from 'zod'

import { DropDown } from '@/components/override/dropdown'
import { FormDateTimePicker } from '@/components/shared/datetime-picker/form-date-time-picker'
import {
  FormDialog,
  FormDialogActions,
  FormDialogButton,
} from '@/components/shared/form-dialog'
import { CameraIcon } from '@/components/shared/icons'
import { ImageCropDialog } from '@/components/shared/image-crop-dialog'
import {
  UnitTreeDropdown,
  type Unit,
} from '@/components/shared/unit-tree-dropdown'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

// Import custom hooks
import { useFormData } from '@/hooks/use-form-data'
import { useUserActions } from '@/hooks/use-user-actions'
import { useAvatarHandling } from '@/shared/hooks'
import { UserGender } from '@c-cam/types'

// Form validation schema
const addUserSchema = z.object({
  code: z.string().optional(), // Optional - backend will auto-generate if empty
  name: z.string().min(1, 'Họ và tên là bắt buộc'),
  email: z.string().email('Email không hợp lệ').min(1, 'Email là bắt buộc'),
  phone: z.string().optional(),
  organizationId: z.string().min(1, 'Tổ chức là bắt buộc'),
  unitId: z.string().min(1, 'Đơn vị là bắt buộc'),
  role: z.string().min(1, 'Vai trò là bắt buộc'),
  birthDate: z.date().optional(),
  gender: z.string().optional(),
  username: z.string().min(1, 'Tên đăng nhập là bắt buộc'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
})

type AddUserFormData = z.infer<typeof addUserSchema>

// Constants
const GENDERS = [
  { id: UserGender.MALE, name: 'Nam' },
  { id: UserGender.FEMALE, name: 'Nữ' },
  { id: UserGender.OTHER, name: 'Khác' },
] as const

// Types
export interface UserData {
  id?: string
  code: string
  name: string
  email: string
  phone: string
  organizationId: string
  unitId: string
  unitName: string
  role: string
  birthDate?: string
  gender?: string
  username: string
  password: string
}

export interface AddUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (userData: UserData, isEdit?: boolean, avatarBlob?: Blob) => void
  editUser?: UserData | null
  mode?: 'create' | 'edit'
  organizationId?: string
}

export function AddUserDialog({
  open,
  onOpenChange,
  onConfirm,
  editUser = null,
  mode = 'create',
  organizationId,
}: AddUserDialogProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [selectedUnit, setSelectedUnit] = useState<Unit | null>(null)

  // Custom hooks
  const {
    organizationOptions,
    roleOptions,
    rolesData,
    isLoadingOrganizations,
    isLoadingRoles,
  } = useFormData()
  const {
    croppedAvatarBlob,
    croppedAvatarUrl,
    selectedImageFile,
    showImageCropDialog,
    setShowImageCropDialog,
    fileInputRef,
    handleAvatarClick,
    handleFileSelect,
    handleCropComplete,
    resetAvatar,
  } = useAvatarHandling()
  const {
    handleCreateMemberWithAvatar,
    handleUpdateMember,
    createUserMutation,
    updateUserMutation,
  } = useUserActions()

  // Loading states
  const isSubmitting =
    createUserMutation.isPending || updateUserMutation.isPending

  const form = useForm<AddUserFormData>({
    resolver: zodResolver(addUserSchema),
    defaultValues: {
      code: '',
      name: '',
      email: '',
      phone: '',
      organizationId: '',
      unitId: '',
      role: '',
      birthDate: undefined,
      gender: '',
      username: '',
      password: '',
    },
  })

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { isValid },
  } = form

  const selectedUnitId = watch('unitId')

  // Populate form when editing
  useEffect(() => {
    if (editUser && mode === 'edit') {
      // Set form values with edit data
      setValue('code', editUser.code || '')
      setValue('name', editUser.name || '')
      setValue('email', editUser.email || '')
      setValue('phone', editUser.phone || '')
      setValue('organizationId', editUser.organizationId || '')
      setValue('unitId', editUser.unitId || '')

      // Set selected unit for display (will be updated when UnitTreeDropdown loads)
      if (editUser.unitId && editUser.unitName) {
        setSelectedUnit({
          id: editUser.unitId,
          name: editUser.unitName,
        } as Unit)
      }

      // Handle role mapping: if editUser.role is a name, find the corresponding ID
      if (editUser.role && rolesData?.roles) {
        const roleByName = rolesData.roles.find(
          (role) => role.name === editUser.role,
        )
        const roleById = rolesData.roles.find(
          (role) => role.id === editUser.role,
        )
        setValue('role', roleByName?.id || roleById?.id || editUser.role)
      } else {
        setValue('role', editUser.role || '')
      }

      setValue('gender', editUser.gender || '')
      setValue('username', editUser.username || '')
      setValue('password', editUser.password || '')

      // Handle birth date conversion
      if (editUser.birthDate) {
        const birthDate = new Date(editUser.birthDate)
        setValue('birthDate', birthDate)
      }
    } else {
      // Reset form for create mode
      reset()
      setSelectedUnit(null)
    }
  }, [editUser, mode, setValue, reset, rolesData])

  const handleUnitChange = (unitId: string, unit?: Unit) => {
    setValue('unitId', unitId || '', { shouldValidate: true })
    setSelectedUnit(unit || null)
  }

  const onSubmit = async (data: AddUserFormData) => {
    try {
      let result

      if (mode === 'edit' && editUser?.id) {
        // Update existing user
        const updateData = {
          unit_id: data.unitId,
          code: data.code,
          name: data.name,
          email: data.email,
          phone: data.phone,
          dob: data.birthDate
            ? data.birthDate.toISOString().split('T')[0]
            : undefined,
          gender: data.gender,
          username: data.username,
          password: data.password,
          member_role_id: data.role,
        }

        result = handleUpdateMember(editUser.id, updateData)
      } else {
        // Create new user
        const createData = {
          unit_id: data.unitId,
          code: data.code || '', // Send empty string if not provided - backend will auto-generate
          name: data.name,
          email: data.email,
          phone: data.phone,
          dob: data.birthDate
            ? data.birthDate.toISOString().split('T')[0]
            : undefined,
          gender: data.gender,
          username: data.username,
          password: data.password,
          member_role_id: data.role,
        }

        console.log('Creating user with data:', createData)
        console.log('Avatar blob:', croppedAvatarBlob ? 'Present' : 'None')

        result = await handleCreateMemberWithAvatar(
          createData,
          croppedAvatarBlob || undefined,
        )

        console.log('Create user result:', result)
      }

      // Only proceed if operation was successful
      if (result) {
        // Reset form and close dialog on success
        reset()
        resetAvatar()
        setSelectedUnit(null)
        onOpenChange(false)

        // Call onConfirm for any additional parent component logic (like refetching data)
        const userData: UserData = {
          id: editUser?.id,
          code: data.code || `E${Date.now()}`,
          name: data.name,
          email: data.email,
          phone: data.phone || '',
          organizationId: data.organizationId,
          unitId: data.unitId,
          unitName: selectedUnit?.name || '',
          role:
            rolesData?.roles?.find((role) => role.id === data.role)?.name ||
            data.role,
          birthDate: data.birthDate
            ? data.birthDate.toISOString().split('T')[0]
            : '',
          gender: data.gender || '',
          username: data.username,
          password: data.password,
        }
        onConfirm(userData, mode === 'edit', croppedAvatarBlob || undefined)
      }
    } catch (error) {
      // Error handling is done by the mutation hooks
      console.error('Error submitting member form:', error)
    }
  }

  const handleCancel = () => {
    reset()
    resetAvatar()
    setSelectedUnit(null)
    onOpenChange(false)
  }

  // Helper components
  const FormField = ({
    label,
    required = false,
    children,
  }: {
    label: string
    required?: boolean
    children: React.ReactNode
  }) => (
    <div className="flex flex-col gap-2">
      <label className="flex items-center gap-1 text-sm font-medium text-[#1f2329]">
        {label}
        {required && <span className="text-[#f54a45]">*</span>}
      </label>
      <div className="relative">{children}</div>
    </div>
  )

  const SectionHeader = ({ title }: { title: string }) => (
    <div className="flex items-center gap-3">
      <div className="w-1 h-5 rounded-sm bg-[#008fd3]" />
      <h3 className="text-base font-semibold text-[#1f2329]">{title}</h3>
    </div>
  )

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={mode === 'edit' ? 'Chỉnh sửa thành viên' : 'Thêm mới thành viên'}
      className="!max-w-[900px] !w-[900px]"
      footer={
        <FormDialogActions>
          <FormDialogButton variant="secondary" onClick={handleCancel}>
            Hủy bỏ
          </FormDialogButton>
          <FormDialogButton
            variant="primary"
            onClick={handleSubmit(onSubmit)}
            disabled={!isValid || isSubmitting}
          >
            {isSubmitting
              ? mode === 'edit'
                ? 'Đang cập nhật...'
                : 'Đang thêm...'
              : mode === 'edit'
                ? 'Cập nhật thành viên'
                : 'Thêm thành viên'}
          </FormDialogButton>
        </FormDialogActions>
      }
    >
      <FormProvider {...form}>
        {/* Phần 1: Thông tin cơ bản */}
        <div className="flex flex-col gap-6 w-full h-full justify-between">
          {/* Phần 1: Thông tin cơ bản */}
          <div className="flex flex-col gap-6 flex-1">
            <SectionHeader title="Thông tin cơ bản" />

            {/* Avatar Upload Section */}
            <div className="flex flex-col gap-3">
              <div
                className="flex items-center justify-center w-20 h-20 rounded-full bg-gray-100 border-2 border-dashed border-gray-300 hover:border-[#008fd3] transition-colors cursor-pointer overflow-hidden"
                onClick={handleAvatarClick}
              >
                {croppedAvatarUrl ? (
                  <img
                    src={croppedAvatarUrl}
                    alt="Avatar preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <CameraIcon />
                )}
              </div>
              <p
                className="text-sm font-medium text-[#008fd3] cursor-pointer hover:underline"
                onClick={handleAvatarClick}
              >
                {croppedAvatarUrl
                  ? 'Thay đổi ảnh đại diện'
                  : 'Thêm ảnh đại diện'}
              </p>
              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>

            {/* Form Fields */}
            <div className="flex flex-col gap-6">
              {/* Row 1: Mã thành viên và Họ và tên */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField label="Mã thành viên">
                  <Input
                    type="text"
                    {...register('code')}
                    placeholder="Để trống để tự động tạo mã (E00001, E00002, ...)"
                  />
                </FormField>
                <FormField label="Họ và tên" required>
                  <Input
                    type="text"
                    {...register('name')}
                    placeholder="Nhập họ và tên"
                  />
                </FormField>
              </div>

              {/* Row 2: Email và Số điện thoại */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField label="Email" required>
                  <Input
                    type="email"
                    {...register('email')}
                    placeholder="Nhập thông tin email"
                  />
                </FormField>
                <FormField label="Số điện thoại">
                  <Input
                    type="tel"
                    {...register('phone')}
                    placeholder="Nhập số điện thoại"
                  />
                </FormField>
              </div>

              {/* Row 3: Tổ chức, Vai trò | Đơn vị, Ngày sinh, Giới tính */}
              <div className="flex gap-6">
                {/* Left side: Tổ chức và Vai trò */}
                <div className="flex flex-col gap-6 flex-1">
                  <div className="flex flex-col gap-2">
                    <label className="flex items-center gap-1 text-sm font-medium text-[#1f2329]">
                      Tổ chức
                      <span className="text-[#f54a45]">*</span>
                    </label>
                    <div className="relative">
                      <DropDown
                        options={organizationOptions}
                        value={watch('organizationId')}
                        onValueChange={(value) =>
                          setValue('organizationId', value, {
                            shouldValidate: true,
                          })
                        }
                        placeholder="Chọn tổ chức"
                        searchPlaceholder="Tìm kiếm tổ chức..."
                        emptyMessage={
                          isLoadingOrganizations
                            ? 'Đang tải...'
                            : 'Không tìm thấy tổ chức'
                        }
                        disabled={isLoadingOrganizations}
                        className="w-full px-3 py-2.5 text-sm bg-white border border-[#d0d3d6] rounded-md focus:outline-none focus:ring-2 focus:ring-[#008fd3] focus:border-transparent transition-all"
                        width="100%"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <label className="flex items-center gap-1 text-sm font-medium text-[#1f2329]">
                      Vai trò
                      <span className="text-[#f54a45]">*</span>
                    </label>
                    <div className="relative">
                      <DropDown
                        options={roleOptions}
                        value={watch('role')}
                        onValueChange={(value) =>
                          setValue('role', value, { shouldValidate: true })
                        }
                        placeholder="Chọn vai trò"
                        searchPlaceholder="Tìm kiếm vai trò..."
                        emptyMessage={
                          isLoadingRoles
                            ? 'Đang tải...'
                            : 'Không tìm thấy vai trò'
                        }
                        disabled={isLoadingRoles}
                        className="w-full px-3 py-2.5 text-sm bg-white border border-[#d0d3d6] rounded-md focus:outline-none focus:ring-2 focus:ring-[#008fd3] focus:border-transparent transition-all"
                        width="100%"
                      />
                    </div>
                  </div>
                </div>

                {/* Right side: Đơn vị, Ngày sinh, Giới tính */}
                <div className="flex flex-col gap-6 flex-1">
                  <div className="flex flex-col gap-2">
                    <label className="flex items-center gap-1 text-sm font-medium text-[#1f2329]">
                      Đơn vị
                      <span className="text-[#f54a45]">*</span>
                    </label>
                    <div className="relative">
                      <UnitTreeDropdown
                        value={selectedUnitId}
                        onValueChange={handleUnitChange}
                        placeholder="Chọn đơn vị"
                        searchPlaceholder="Tìm kiếm đơn vị..."
                        className="w-full"
                        organizationId={organizationId}
                        width="100%"
                      />
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <div className="flex flex-col gap-2 flex-1">
                      <label className="text-sm font-medium text-[#1f2329]">
                        Ngày sinh
                      </label>
                      <div className="relative">
                        <FormDateTimePicker
                          control={form.control}
                          name="birthDate"
                        />
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 flex-1">
                      <label className="text-sm font-medium text-[#1f2329]">
                        Giới tính
                      </label>
                      <div className="relative">
                        <Select
                          onValueChange={(value) =>
                            setValue('gender', value, { shouldValidate: true })
                          }
                        >
                          <SelectTrigger className="w-full px-3 py-2.5 text-sm bg-white border border-[#d0d3d6] rounded-md focus:outline-none focus:ring-2 focus:ring-[#008fd3] focus:border-transparent transition-all">
                            <SelectValue
                              placeholder="Chọn giới tính"
                              className="text-[#8f959e]"
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {GENDERS.map((gender) => (
                              <SelectItem key={gender.id} value={gender.id}>
                                {gender.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Phần 2: Thông tin đăng nhập */}
            <div className="flex flex-col gap-6 flex-1">
              <SectionHeader title="Thông tin đăng nhập" />

              {/* Form Fields */}
              <div className="flex flex-col gap-6">
                {/* Row 1: Tên đăng nhập và Mật khẩu */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField label="Tên đăng nhập" required>
                    <Input
                      type="text"
                      {...register('username')}
                      placeholder="Nhập tên đăng nhập"
                    />
                  </FormField>
                  <FormField label="Mật khẩu" required>
                    <Input
                      type={showPassword ? 'text' : 'password'}
                      {...register('password')}
                      placeholder="Nhập mật khẩu"
                      className="pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 transition-colors cursor-pointer"
                    >
                      {showPassword ? (
                        <EyeOff className="w-4 h-4" />
                      ) : (
                        <Eye className="w-4 h-4" />
                      )}
                    </button>
                  </FormField>
                </div>
              </div>
            </div>
          </div>

          {/* Phần 3: Thông tin khuôn mặt */}
          <div className="flex flex-col gap-6 flex-1">
            <SectionHeader title="Thông tin khuôn mặt" />

            {/* Face Image Upload */}
            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-2">
                <label className="text-sm font-medium text-[#1f2329]">
                  Ảnh mặt
                </label>
                <div className="flex justify-center items-center w-[94px] h-[94px] p-4 rounded-md bg-white border border-[#d0d3d6] hover:border-[#008fd3] transition-colors cursor-pointer">
                  <p className="text-sm font-medium text-center text-[#008fd3]">
                    Thêm ảnh
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </FormProvider>

      {/* Image Crop Dialog */}
      {selectedImageFile && (
        <ImageCropDialog
          open={showImageCropDialog}
          onOpenChange={setShowImageCropDialog}
          imageSrc={selectedImageFile}
          onCropComplete={handleCropComplete}
          aspectRatio={1} // Square crop for avatar
          title="Cắt ảnh đại diện"
        />
      )}
    </FormDialog>
  )
}
