import { useTenantSelection } from '@/hooks/use-tenant-actions'
import { Notification } from '../override/notification'
import { UserBox } from '../override/userbox'
import { Separator } from '../ui/separator'
import { FloatingMenu } from './floating-menu'
import { Logo } from './logo'
import TenantSelector from './tenant-selector/TenantSelector'

export function HeaderLayout() {
  const { tenantsForSelector, handleTenantChange } = useTenantSelection()

  return (
    <>
      <div className="flex justify-between items-center w-full h-[60px] pr-6 bg-white border-t-0 border-r-0 border-b-[1.5px] border-l-0 border-[#e9eaf2]">
        <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-4 px-6">
          <FloatingMenu />
          <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2">
            <Logo />
          </div>
          {/* Only show separator and TenantSelector if user has more than 1 tenant */}
          {tenantsForSelector.length > 1 && (
            <>
              <Separator orientation="vertical" />
              <TenantSelector
                tenants={tenantsForSelector}
                placeholder="Select organization..."
                onTenantChange={(tenant) => {
                  if (tenant) {
                    handleTenantChange(tenant.id)
                  }
                }}
                onAddTenant={() => {
                  console.log(
                    'Add tenant clicked - handled by TenantSelector internal modal',
                  )
                  // The CreateTenantModal is now handled internally by TenantSelector
                }}
                onTenantCreated={(newTenant) => {
                  console.log('New tenant created in header:', newTenant)
                  // The tenant is already added to the local list and selected by TenantSelector
                  // We could refresh the global tenant list here if needed
                }}
                disabled={tenantsForSelector.length === 0}
                className="min-w-[200px]"
              />
            </>
          )}
        </div>
        <div className="flex justify-end items-center flex-grow-0 flex-shrink-0 gap-4">
          <Notification />
          <UserBox />
        </div>
      </div>
    </>
  )
}

export default HeaderLayout
