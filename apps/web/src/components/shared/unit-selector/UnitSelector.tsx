import { AddUnitDialog } from '@/components/shared/add-unit-dialog'
import { EditUnitDialog } from '@/components/shared/edit-unit-dialog'
import {
  BuildingIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  EmptyIcon,
  MenuDotsVerticalIcon,
  PlusIcon,
  SearchIcon,
  StarIcon,
} from '@/components/shared/icons'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { useSelectedTenant } from '@/hooks/use-selected-tenant'
import { useUnitActions } from '@/hooks/use-unit-actions'
import { Unit, useUnitsByTenantQuery } from '@/hooks/use-unit-query'
import { cn } from '@/utils/cn'
import React, { useEffect, useMemo, useState } from 'react'

// Types
interface TreeNodeState {
  expandedKeys: string[]
  selectedUnit?: Unit
  filteredData: Unit[]
  searchQuery: string
}

interface UnitSelectorProps {
  data?: Unit[]
  onSelectUnit?: (unit?: Unit) => void
  selectedUnitId?: string
  organizationName?: string
  searchPlaceholder?: string
  className?: string
  useTenantData?: boolean
  showAddButton?: boolean // New prop to control if add button should be shown
}

// Type for ComboboxUnit
interface ComboboxUnit {
  id: string
  name: string
  parentId?: string
}

// Utility functions
const buildUnitTree = (units: Unit[]): Unit[] => {
  if (!units || units.length === 0) return []

  const unitMap = new Map<string, Unit>()
  const rootUnits: Unit[] = []

  // Create a map of all units and initialize children arrays
  units.forEach((unit) => {
    unitMap.set(unit.id, { ...unit, children: [] })
  })

  // Build the tree structure
  units.forEach((unit) => {
    const unitWithChildren = unitMap.get(unit.id)!

    if (unit.parent_unit_id && unitMap.has(unit.parent_unit_id)) {
      // This unit has a parent, add it to parent's children
      const parent = unitMap.get(unit.parent_unit_id)!
      parent.children = parent.children || []
      parent.children.push(unitWithChildren)
    } else {
      // This is a root unit
      rootUnits.push(unitWithChildren)
    }
  })

  return rootUnits
}

const filterUnitsByKeyword = (units: Unit[], keyword: string) => {
  const filteredUnits: Unit[] = []
  const expandedKeys: string[] = []

  const searchInUnit = (unit: Unit): Unit | null => {
    const matchesKeyword = unit.name
      .toLowerCase()
      .includes(keyword.toLowerCase())
    const filteredChildren: Unit[] = []

    // Search in children
    if (unit.children) {
      unit.children.forEach((child) => {
        const filteredChild = searchInUnit(child)
        if (filteredChild) {
          filteredChildren.push(filteredChild)
          expandedKeys.push(unit.id) // Expand parent if child matches
        }
      })
    }

    // Include unit if it matches or has matching children
    if (matchesKeyword || filteredChildren.length > 0) {
      return {
        ...unit,
        children: filteredChildren,
      }
    }

    return null
  }

  units.forEach((unit) => {
    const filteredUnit = searchInUnit(unit)
    if (filteredUnit) {
      filteredUnits.push(filteredUnit)
    }
  })

  return { filteredUnits, expandedKeys }
}

const getTruncatedName = (name: string, depth: number): string => {
  const maxLength = Math.max(20 - depth * 2, 10)
  return name.length > maxLength ? `${name.substring(0, maxLength)}...` : name
}

const UnitSelector: React.FC<UnitSelectorProps> = ({
  data,
  onSelectUnit,
  selectedUnitId,
  organizationName, // This will be overridden by selectedTenant.name
  searchPlaceholder = 'Theo tên đơn vị',
  className,
  useTenantData = false, // New prop to enable tenant-based data fetching
  showAddButton = true, // Show add button by default
}) => {
  const [state, setState] = useState<TreeNodeState>({
    expandedKeys: [],
    selectedUnit: undefined,
    filteredData: [],
    searchQuery: '',
  })

  // Dialog states
  const [addUnitDialogOpen, setAddUnitDialogOpen] = useState(false)
  const [editUnitDialogOpen, setEditUnitDialogOpen] = useState(false)
  const [unitForEdit, setUnitForEdit] = useState<Unit | null>(null)
  const [parentUnitForAdd, setParentUnitForAdd] = useState<Unit | null>(null)

  // Get selected tenant from localStorage
  const { selectedTenant, selectedTenantId } = useSelectedTenant()

  // Simple re-render trigger when selectedTenant changes
  // This ensures the component re-renders when tenant changes
  useEffect(() => {
    // This effect will run whenever selectedTenant or selectedTenantId changes
    // No need for complex event listeners
  }, [selectedTenant, selectedTenantId])

  // Unit actions hook
  const { handleUpdateUnit } = useUnitActions()

  // Fetch units by tenant if useTenantData is true or if we have a selectedTenant
  const shouldFetchTenantData = useTenantData || !!selectedTenantId

  const { data: tenantUnitsData, refetch: refetchUnits } =
    useUnitsByTenantQuery(shouldFetchTenantData)

  // Use selectedTenant.name as organizationName, fallback to prop or default
  const displayOrganizationName =
    selectedTenant?.name || organizationName || 'Tổ chức'

  // Determine which data to use
  const unitsData = shouldFetchTenantData
    ? tenantUnitsData?.units || []
    : data || []

  // Build tree structure from flat data
  const treeData = useMemo(() => buildUnitTree(unitsData), [unitsData])

  // Track previous tenant ID to avoid unnecessary refetches
  const [prevTenantId, setPrevTenantId] = useState<string | null>(null)

  // Effect to handle tenant changes
  useEffect(() => {
    // Only trigger when tenant actually changes, not on initial load
    if (selectedTenantId !== prevTenantId && prevTenantId !== null) {
      setPrevTenantId(selectedTenantId)

      // Only reset when tenant actually changes (not on initial load)
      if (shouldFetchTenantData) {
        // Reset state when tenant changes
        setState((prev) => ({
          ...prev,
          selectedUnit: undefined,
          searchQuery: '',
          expandedKeys: [],
          filteredData: [],
        }))

        // Clear selected unit in parent component
        onSelectUnit?.(undefined)

        // React Query will automatically refetch when tenantId changes in query key
      }
    } else if (prevTenantId === null) {
      // Initialize prevTenantId on first load
      setPrevTenantId(selectedTenantId)
    }
  }, [selectedTenantId, prevTenantId, shouldFetchTenantData, onSelectUnit])

  // Initialize filtered data when tree data changes
  useEffect(() => {
    setState((prev) => ({
      ...prev,
      filteredData: treeData,
      // Reset search when new data comes in
      searchQuery: prev.searchQuery ? prev.searchQuery : '',
      expandedKeys: prev.searchQuery ? prev.expandedKeys : [],
    }))
  }, [treeData])

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keyword = e.target.value
    setState((prev) => ({ ...prev, searchQuery: keyword }))

    if (keyword.trim()) {
      const { filteredUnits, expandedKeys } = filterUnitsByKeyword(
        treeData,
        keyword,
      )
      setState((prev) => ({
        ...prev,
        filteredData: filteredUnits,
        expandedKeys,
      }))
    } else {
      setState((prev) => ({
        ...prev,
        filteredData: treeData,
        expandedKeys: [],
      }))
    }
  }

  // Handle expand/collapse
  const handleToggleExpand = (unitId: string) => {
    setState((prev) => ({
      ...prev,
      expandedKeys: prev.expandedKeys.includes(unitId)
        ? prev.expandedKeys.filter((key) => key !== unitId)
        : [...prev.expandedKeys, unitId],
    }))
  }

  // Handle unit selection
  const handleSelectUnit = (unit?: Unit) => {
    setState((prev) => ({ ...prev, selectedUnit: unit }))
    onSelectUnit?.(unit)
  }

  // Handle unit menu actions
  const handleEditUnit = (unit: Unit, e: React.MouseEvent) => {
    e.stopPropagation()
    setUnitForEdit(unit)
    setEditUnitDialogOpen(true)
  }

  const handleAddSubUnit = (unit: Unit, e: React.MouseEvent) => {
    e.stopPropagation()
    setParentUnitForAdd(unit)
    setAddUnitDialogOpen(true)
  }

  // Handle add unit
  const handleAddUnit = () => {
    setParentUnitForAdd(null) // Reset parent unit for regular add
    setAddUnitDialogOpen(true)
  }

  // Handle add unit success
  const handleAddUnitSuccess = () => {
    console.log('Unit created successfully')
    // Reset parent unit after successful creation
    setParentUnitForAdd(null)
    // Refresh units data to show the new unit
    refetchUnits()
  }

  // Handle edit unit confirmation
  const handleEditUnitConfirm = (unitId: string, newName: string) => {
    console.log('Updating unit:', unitId, 'with new name:', newName)

    // Call API to update unit name
    handleUpdateUnit(unitId, { name: newName })

    // Close dialog and reset state
    setEditUnitDialogOpen(false)
    setUnitForEdit(null)
  }

  // Convert units for AddUnitDialog
  const convertToComboboxUnit = (unit: Unit): ComboboxUnit => {
    return {
      id: unit.id,
      name: unit.name,
      parentId: unit.parent_unit_id,
    }
  }

  // Render tree node
  const renderTreeNode = (unit: Unit, depth = 0): React.ReactNode => {
    const isExpanded = state.expandedKeys.includes(unit.id)
    const isSelected =
      state.selectedUnit?.id === unit.id || selectedUnitId === unit.id
    const hasChildren = unit.children && unit.children.length > 0

    return (
      <div key={unit.id}>
        <div
          className={cn(
            'cursor-pointer rounded-lg transition-colors',
            isSelected ? 'bg-[#008FD3]/10' : 'hover:bg-gray-50',
          )}
        >
          <div className="flex items-center h-[42px] w-full px-2">
            <div
              style={{ paddingLeft: `${depth * 16}px` }}
              className="flex items-center flex-1"
            >
              {/* Expand/Collapse button */}
              <button
                onClick={() => handleToggleExpand(unit.id)}
                className={cn(
                  'text-sm mr-2 cursor-pointer transition-colors',
                  hasChildren ? 'visible' : 'invisible',
                  isSelected ? 'text-[#008FD3]' : 'text-gray-600',
                )}
              >
                {isExpanded ? (
                  <ChevronDownIcon width={12} height={12} />
                ) : (
                  <ChevronRightIcon width={12} height={12} />
                )}
              </button>

              {/* Unit content */}
              <div
                className="flex-1 flex items-center cursor-pointer"
                onClick={() => handleSelectUnit(unit)}
              >
                <BuildingIcon
                  className={cn(
                    'mr-2 transition-colors',
                    isSelected ? 'text-[#008FD3]' : 'text-gray-600',
                  )}
                  width={14}
                  height={14}
                />
                <span
                  className={cn(
                    'text-sm transition-colors',
                    isSelected
                      ? 'font-semibold text-[#008FD3]'
                      : 'text-gray-900',
                  )}
                  title={unit.name}
                >
                  {getTruncatedName(unit.name, depth)}
                </span>
              </div>

              {/* Unit menu */}
              <Popover>
                <PopoverTrigger asChild>
                  <button
                    className="p-1 rounded transition-colors"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MenuDotsVerticalIcon className="w-1 h-4" />
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2" align="end">
                  <div className="space-y-1">
                    <button
                      className="cursor-pointer w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors"
                      onClick={(e) => handleEditUnit(unit, e)}
                    >
                      Chỉnh sửa
                    </button>
                    <button
                      className="cursor-pointer w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors"
                      onClick={(e) => handleAddSubUnit(unit, e)}
                    >
                      Thêm bộ phận phụ
                    </button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* Children */}
        {isExpanded && hasChildren && (
          <div>
            {unit.children!.map((child) => renderTreeNode(child, depth + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={cn('mb-2', className)}>
      {/* Search input */}
      <div className="mb-4">
        <div className="relative">
          <SearchIcon
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            width={16}
            height={16}
          />
          <Input
            className="pl-10"
            value={state.searchQuery}
            placeholder={searchPlaceholder}
            onChange={handleSearch}
          />
        </div>
      </div>

      {/* Organization root */}
      <div
        className={cn(
          'rounded-lg transition-colors cursor-pointer',
          !state.selectedUnit ? 'bg-[#008FD3]/10' : 'hover:bg-gray-50',
        )}
        onClick={() => handleSelectUnit()}
      >
        <div className="flex py-3 px-4 h-[42px] justify-between items-center">
          <span
            className={cn(
              'font-bold transition-colors',
              !state.selectedUnit ? 'text-[#008FD3]' : 'text-gray-900',
            )}
          >
            {displayOrganizationName}
          </span>
          <StarIcon
            className={cn(
              'w-5 h-5 p-0.5 border-2 rounded-full transition-colors',
              !state.selectedUnit
                ? 'text-[#008FD3] border-[#008FD3]'
                : 'text-gray-600 border-gray-600',
            )}
            width={16}
            height={16}
          />
        </div>
      </div>

      {/* Tree content */}
      <div className="max-h-[400px] overflow-y-auto mt-2">
        {state.filteredData.length > 0 ? (
          <div>{state.filteredData.map((unit) => renderTreeNode(unit))}</div>
        ) : (
          <div className="text-center py-8">
            <EmptyIcon
              className="mx-auto text-gray-400 mb-3"
              width={40}
              height={40}
            />
            <p className="text-gray-500 text-sm">Không có dữ liệu</p>
          </div>
        )}
      </div>

      {/* Add Unit Button */}
      {showAddButton && (
        <div className="mt-2">
          <button
            onClick={handleAddUnit}
            className="flex justify-center items-center flex-grow h-8 py-2 rounded-md bg-white border border-[#d0d3d6] w-full hover:bg-[#008FD3]/5 hover:border-[#008FD3] transition-all duration-200 group cursor-pointer"
          >
            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 pr-1">
              <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                <PlusIcon
                  className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                  fillColor="#1F2329"
                />
              </div>
            </div>
            <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative overflow-hidden">
              <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#1f2329] group-hover:text-[#008FD3]">
                Thêm đơn vị
              </p>
            </div>
          </button>
        </div>
      )}

      {/* Add Unit Dialog */}
      <AddUnitDialog
        open={addUnitDialogOpen}
        onOpenChange={setAddUnitDialogOpen}
        onSuccess={handleAddUnitSuccess}
        parentUnit={
          parentUnitForAdd ? convertToComboboxUnit(parentUnitForAdd) : null
        }
        selectedTenant={selectedTenant}
      />

      {/* Edit Unit Dialog */}
      <EditUnitDialog
        open={editUnitDialogOpen}
        onOpenChange={setEditUnitDialogOpen}
        unit={unitForEdit}
        onConfirm={handleEditUnitConfirm}
      />
    </div>
  )
}

export default UnitSelector
