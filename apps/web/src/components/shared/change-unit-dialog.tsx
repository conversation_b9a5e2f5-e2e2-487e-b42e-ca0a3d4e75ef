import {
  FormDialog,
  FormDialogActions,
  FormDialogButton,
  FormDialogField,
  FormDialogMessage,
} from '@/components/shared/form-dialog'
import type { Unit as ComboboxUnit } from '@/components/shared/unit-tree-combobox'
import { UnitTreeCombobox } from '@/components/shared/unit-tree-combobox'
import { useSelectedTenant } from '@/hooks/use-selected-tenant'
import { useUnitActions } from '@/hooks/use-unit-actions'
import type { Unit as UnitSelectorUnit } from '@/hooks/use-unit-query'
import { useUnitsByTenantQuery } from '@/hooks/use-unit-query'
import { useState } from 'react'

interface User {
  id: string
  unit_id: string
  member_role_id?: string
  code: string
  name: string
  email?: string
  phone?: string
  dob?: string
  gender?: string
  username: string
  status: 'active' | 'inactive'
  created_by: string
  createdAt: string
  updatedAt: string
  avatar_id?: string
  face_id?: string
  // Populated fields from backend
  tenant?: {
    id: string
    name: string
  }
  unit?: {
    id: string
    name: string
  }
}

interface ChangeUnitDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedCount: number
  onConfirm: (selectedUnit: UnitSelectorUnit, userIds: string[]) => void
  userIds: string[] // List of user IDs to update
  singleUser?: User | null // For single user unit change
  selectedUnitId?: string // Current selected unit ID for checking if users should be removed from view
  clearSelection?: () => void // Function to clear checkbox selections
}

// Convert UnitSelector Unit to Combobox Unit
function convertToComboboxUnit(unit: UnitSelectorUnit): ComboboxUnit {
  return {
    id: unit.id,
    name: unit.name,
    parentId: unit.parent_unit_id,
  }
}

export function ChangeUnitDialog({
  open,
  onOpenChange,
  selectedCount,
  onConfirm,
  userIds,
  singleUser = null,
  selectedUnitId: currentSelectedUnitId,
  clearSelection,
}: ChangeUnitDialogProps) {
  const [selectedUnitId, setSelectedUnitId] = useState<string>()
  const [selectedUnit, setSelectedUnit] = useState<UnitSelectorUnit>()

  // Get selected tenant from localStorage
  const { selectedTenantId } = useSelectedTenant()

  // Fetch units by tenant (tenant ID is automatically retrieved from access token)
  const { data: tenantUnitsData } = useUnitsByTenantQuery(!!selectedTenantId)
  const units = tenantUnitsData?.units || []

  // Get unit actions hook
  const { handleUpdateUsersUnit, isUpdatingUsersUnit } = useUnitActions()

  // Convert units for combobox
  const comboboxUnits = units.map(convertToComboboxUnit)

  const handleUnitChange = (
    unitId: string | undefined,
    unit: ComboboxUnit | undefined,
  ) => {
    setSelectedUnitId(unitId)
    // Find the original unit from the units array
    const originalUnit = units.find((u: UnitSelectorUnit) => u.id === unitId)
    setSelectedUnit(originalUnit)
  }

  const handleConfirm = () => {
    if (selectedUnit && selectedUnitId) {
      // Prepare new unit info for cache updates
      const newUnitInfo = {
        id: selectedUnit.id,
        name: selectedUnit.name,
      }

      // Call API to update users' unit with additional context
      handleUpdateUsersUnit(
        userIds,
        selectedUnitId,
        newUnitInfo,
        currentSelectedUnitId,
        clearSelection,
      )
      onOpenChange(false)
      // Reset selection
      setSelectedUnitId(undefined)
      setSelectedUnit(undefined)
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    // Reset selection
    setSelectedUnitId(undefined)
    setSelectedUnit(undefined)
  }

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Thay đổi đơn vị"
      footer={
        <FormDialogActions>
          <FormDialogButton variant="secondary" onClick={handleCancel}>
            Hủy bỏ
          </FormDialogButton>
          <FormDialogButton
            variant="primary"
            onClick={handleConfirm}
            disabled={!selectedUnit || isUpdatingUsersUnit}
          >
            {isUpdatingUsersUnit ? 'Đang cập nhật...' : 'Xác nhận'}
          </FormDialogButton>
        </FormDialogActions>
      }
      className="!h-[280px]"
    >
      <div className="flex flex-col justify-start items-start w-full gap-4">
        <FormDialogField label="Đơn vị mới" required={true}>
          <UnitTreeCombobox
            units={comboboxUnits}
            value={selectedUnitId}
            onValueChange={handleUnitChange}
            placeholder="Chọn đơn vị"
            searchPlaceholder="Tìm kiếm đơn vị..."
            className="w-full"
          />
        </FormDialogField>

        <FormDialogMessage>
          <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]">
            <span className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]">
              Hệ thống sẽ thực hiện thay đổi đơn vị của{' '}
            </span>
            {singleUser && selectedCount === 1 ? (
              <span className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#23262f]">
                {singleUser.name} - {singleUser.code}
              </span>
            ) : (
              <>
                <span className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#23262f]">
                  {selectedCount} thành viên{' '}
                </span>
                <span className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f]">
                  đã chọn
                </span>
              </>
            )}
          </p>
          <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#23262f] mt-1">
            Bạn có chắc chắn muốn tiếp tục cập nhật đơn vị?
          </p>
        </FormDialogMessage>
      </div>
    </FormDialog>
  )
}
