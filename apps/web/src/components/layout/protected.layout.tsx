import { useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'
import HeaderLayout from '../shared/header'
import { SiderLayout } from '../shared/sider'
import { SidebarProvider } from '../ui/sidebar'

import { useGlobalLoading } from '@/hooks/use-global-loading'
import { useIdentityActions } from '@/hooks/use-identity-actions'
import { cn } from '@/utils/cn'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

export default function ProtectedLayout({
  children,
  className,
  ...props
}: Props) {
  const { isAuthenticated, isLoading } = useIdentityActions()
  const { showLoading, hideLoading } = useGlobalLoading()
  const navigate = useNavigate()

  // Show loading for authentication check
  useEffect(() => {
    let loadingId: string | null = null

    if (isLoading) {
      // Show loading with high priority for authentication
      loadingId = showLoading('<PERSON><PERSON> kiểm tra xác thực...', 8)
    }

    return () => {
      // Cleanup loading when component unmounts or loading state changes
      if (loadingId) {
        hideLoading(loadingId)
      }
    }
  }, [isLoading, showLoading, hideLoading])

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate({ to: '/auth/login' })
    }
  }, [isAuthenticated, isLoading, navigate])

  // Don't render anything while loading (global loading will handle the UI)
  if (isLoading) {
    return null
  }

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return null
  }

  return (
    <div
      className={cn('h-screen flex flex-col overflow-hidden', className)}
      {...props}
    >
      {/* Header section - full width, positioned at the top */}
      <div className="w-full z-50 flex-shrink-0">
        <HeaderLayout />
      </div>

      {/* Body section with fixed height */}
      <div className="flex flex-1 overflow-hidden">
        <SidebarProvider>
          {/* Sidebar and content container */}
          <div className="flex w-full h-full">
            {/* Sidebar with fixed height */}
            <div className="h-full flex-shrink-0">
              <SiderLayout />
            </div>

            {/* Main content - only this will scroll */}
            <main className="flex-1 overflow-auto bg-[#f5f6f7] transition-all duration-200">
              {children}
            </main>
          </div>
        </SidebarProvider>
      </div>
    </div>
  )
}
