import { useGlobalLoading } from '@/hooks/use-global-loading'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

/**
 * Component demo để test Loading Provider với các tính năng nâng cao
 * 
 * Features được test:
 * - Debounced loading (200ms delay)
 * - Minimum display time (500ms)
 * - Priority-based messages
 * - Multiple concurrent loading
 * - Smooth transitions
 */
export function LoadingDemo() {
  const {
    showLoading,
    hideLoading,
    showCriticalMessage,
    clearAllLoading,
    isLoading,
    isVisible,
    loadingCount,
    withLoading,
    simulateApiCall,
    simulateMultipleApiCalls,
  } = useGlobalLoading()

  const handleQuickTest = async () => {
    // Test debouncing - loading này sẽ không hiện vì quá nhanh
    const loadingId = showLoading('Test nhanh...', 0)
    
    try {
      await new Promise(resolve => setTimeout(resolve, 100))
      console.log('Quick test completed - loading should not have shown')
    } finally {
      hideLoading(loadingId)
    }
  }

  const handleMinimumDisplayTest = async () => {
    // Test minimum display time - loading sẽ hiện ít nhất 500ms
    await withLoading(
      async () => {
        await new Promise(resolve => setTimeout(resolve, 200))
        console.log('Short task completed - but loading will show for minimum 500ms')
      },
      { message: 'Test thời gian hiển thị tối thiểu...', priority: 1 }
    )
  }

  const handlePriorityTest = async () => {
    // Test priority - message có priority cao hơn sẽ hiện trước
    const lowPriorityId = showLoading('Priority thấp...', 1)
    
    setTimeout(() => {
      const highPriorityId = showLoading('Priority cao!', 5)
      
      setTimeout(() => {
        hideLoading(highPriorityId)
        hideLoading(lowPriorityId)
      }, 2000)
    }, 1000)
  }

  const handleCriticalTest = async () => {
    // Test critical loading (priority 10)
    const criticalId = showCriticalMessage('Đang xử lý quan trọng!')
    
    setTimeout(() => {
      hideLoading(criticalId)
    }, 3000)
  }

  const handleMultipleTest = async () => {
    // Test multiple concurrent API calls
    try {
      const results = await simulateMultipleApiCalls()
      console.log('Multiple API calls completed:', results)
    } catch (error) {
      console.error('Multiple API calls failed:', error)
    }
  }

  const handleRapidTest = async () => {
    // Test rapid loading calls - should not flicker
    const promises = []
    
    for (let i = 0; i < 5; i++) {
      promises.push(
        simulateApiCall(200 + i * 100, `Tác vụ ${i + 1}...`, i)
      )
    }
    
    try {
      const results = await Promise.allSettled(promises)
      console.log('Rapid test completed:', results)
    } catch (error) {
      console.error('Rapid test failed:', error)
    }
  }

  const handleLongTest = async () => {
    // Test long loading
    await simulateApiCall(5000, 'Đang xử lý dữ liệu lớn...', 3)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Loading Provider Demo</CardTitle>
          <CardDescription>
            Test các tính năng nâng cao của Loading Provider
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Loading Status */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Trạng thái:</span>
              <Badge variant={isLoading ? "default" : "secondary"}>
                {isLoading ? "Đang loading" : "Không loading"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Hiển thị:</span>
              <Badge variant={isVisible ? "default" : "outline"}>
                {isVisible ? "Đang hiện" : "Đã ẩn"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Số lượng:</span>
              <Badge variant="outline">{loadingCount}</Badge>
            </div>
          </div>

          <Separator />

          {/* Basic Tests */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Basic Tests</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button onClick={handleQuickTest} variant="outline" size="sm">
                Test Nhanh (100ms)
              </Button>
              <Button onClick={handleMinimumDisplayTest} variant="outline" size="sm">
                Test Min Display
              </Button>
              <Button onClick={handleLongTest} variant="outline" size="sm">
                Test Dài (5s)
              </Button>
              <Button onClick={clearAllLoading} variant="destructive" size="sm">
                Clear All
              </Button>
            </div>
          </div>

          <Separator />

          {/* Priority Tests */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Priority Tests</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              <Button onClick={handlePriorityTest} variant="outline" size="sm">
                Test Priority
              </Button>
              <Button onClick={handleCriticalTest} variant="outline" size="sm">
                Test Critical
              </Button>
              <Button 
                onClick={() => simulateApiCall(2000, 'API Call thường...', 1)} 
                variant="outline" 
                size="sm"
              >
                API Call
              </Button>
            </div>
          </div>

          <Separator />

          {/* Advanced Tests */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Advanced Tests</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              <Button onClick={handleMultipleTest} variant="outline" size="sm">
                Multiple API Calls
              </Button>
              <Button onClick={handleRapidTest} variant="outline" size="sm">
                Rapid Loading Test
              </Button>
              <Button 
                onClick={() => {
                  // Test concurrent different priorities
                  simulateApiCall(1000, 'Low priority...', 1)
                  simulateApiCall(1500, 'Medium priority...', 3)
                  simulateApiCall(2000, 'High priority...', 5)
                }} 
                variant="outline" 
                size="sm"
              >
                Concurrent Priority
              </Button>
            </div>
          </div>

          <Separator />

          {/* Manual Controls */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Manual Controls</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button 
                onClick={() => showLoading('Manual loading...', 0)} 
                variant="outline" 
                size="sm"
              >
                Show Loading
              </Button>
              <Button 
                onClick={() => showCriticalMessage('Critical message!')} 
                variant="outline" 
                size="sm"
              >
                Show Critical
              </Button>
              <Button 
                onClick={() => {
                  const id1 = showLoading('Message 1...', 1)
                  const id2 = showLoading('Message 2...', 2)
                  const id3 = showLoading('Message 3...', 3)
                  
                  setTimeout(() => hideLoading(id1), 1000)
                  setTimeout(() => hideLoading(id2), 2000)
                  setTimeout(() => hideLoading(id3), 3000)
                }} 
                variant="outline" 
                size="sm"
              >
                Staggered Loading
              </Button>
              <Button onClick={clearAllLoading} variant="destructive" size="sm">
                Clear All
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Hướng dẫn Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p><strong>Test Nhanh:</strong> Loading sẽ không hiện vì task hoàn thành trong 100ms (dưới 200ms delay)</p>
          <p><strong>Test Min Display:</strong> Loading sẽ hiện ít nhất 500ms dù task chỉ mất 200ms</p>
          <p><strong>Test Priority:</strong> Message có priority cao hơn sẽ hiển thị trước</p>
          <p><strong>Multiple API Calls:</strong> Nhiều API calls cùng lúc, message sẽ thay đổi theo priority</p>
          <p><strong>Rapid Loading:</strong> 5 loading calls liên tiếp, không bị chớp nháy</p>
          <p><strong>Concurrent Priority:</strong> 3 loading cùng lúc với priority khác nhau</p>
        </CardContent>
      </Card>
    </div>
  )
}
