/**
 * Browser tracing initialization for the web application
 */

import { tracingConfig } from './configs/tracing.config'

/**
 * Initialize distributed tracing for the web application
 */
export async function initializeBrowserTracing(): Promise<void> {
  try {
    // Check if tracing is enabled before initializing
    if (!tracingConfig.enabled) {
      console.log('Distributed tracing is disabled, skipping initialization')
      return
    }

    console.log('Initializing distributed tracing for web application...')

    const { initializeBrowserTracing: initBrowserTracingLib } = await import('@c-cam/tracing-browser')
    await initBrowserTracingLib(tracingConfig)

    console.log('Distributed tracing initialized successfully', {
      serviceName: tracingConfig.serviceName,
      environment: tracingConfig.environment,
      enabled: tracingConfig.enabled,
    })
  } catch (error) {
    console.error('Failed to initialize distributed tracing', error)
    // Don't throw error to prevent application startup failure
    // Tracing is important but not critical for basic functionality
  }
}

/**
 * Get the global browser tracing instance
 */
export async function getBrowserTracing() {
  try {
    // Check if tracing is enabled before attempting to get instance
    if (!tracingConfig.enabled) {
      return null
    }

    const { getBrowserTracing: getBrowserTracingLib } = await import('@c-cam/tracing-browser')
    return getBrowserTracingLib()
  } catch (error) {
    console.warn('Failed to get browser tracing instance:', error)
    return null
  }
}

/**
 * Get the global tracer instance
 * Use this to create custom spans in your application code
 */
export async function getWebTracer() {
  try {
    // Check if tracing is enabled before attempting to get tracer
    if (!tracingConfig.enabled) {
      return null
    }

    const { getWebTracer: getWebTracerLib } = await import('@c-cam/tracing-browser')
    return getWebTracerLib()
  } catch (error) {
    console.warn('Tracer not available, tracing may not be initialized', error)
    return null
  }
}

/**
 * Create a custom span for web operations
 * This is a convenience function for common web tracing patterns
 */
export async function withWebSpan<T>(
  name: string,
  operation: () => Promise<T> | T,
  attributes?: Record<string, any>,
): Promise<T> {
  const tracer = await getWebTracer()

  if (!tracer) {
    // If tracing is not available, just execute the operation
    return await operation()
  }

  return tracer.withSpan(
    {
      name: `web.${name}`,
      attributes: {
        'web.operation': name,
        'service.name': 'c-cam-web',
        'page.url': window.location.href,
        'page.title': document.title,
        ...attributes,
      },
    },
    operation,
  )
}

/**
 * Set user context for tracing
 * Call this after user authentication to enrich traces with user information
 */
export async function setUserContextForTracing(userContext: {
  userId?: string
  userEmail?: string
  sessionId?: string
}): Promise<void> {
  const browserTracing = await getBrowserTracing()

  if (browserTracing && userContext.userId) {
    browserTracing.setUserContext({
      userId: userContext.userId,
      email: userContext.userEmail,
      sessionId: userContext.sessionId,
      attributes: {
        userAgent: navigator.userAgent,
      },
    })
  }
}

/**
 * Track a custom event
 * Use this to track user interactions and business events
 */
export async function trackEvent(
  name: string,
  attributes?: Record<string, any>,
): Promise<void> {
  const browserTracing = await getBrowserTracing()

  if (browserTracing) {
    browserTracing.trackEvent(name, {
      'event.timestamp': Date.now(),
      'page.url': window.location.href,
      'page.title': document.title,
      ...attributes,
    })
  }
}

/**
 * Track a page view
 * Use this for navigation tracking
 */
export async function trackPageView(
  pageName?: string,
  attributes?: Record<string, any>,
): Promise<void> {
  const tracer = await getWebTracer()

  if (tracer) {
    tracer.withSpan(
      {
        name: 'page_view',
        attributes: {
          'page.name': pageName || document.title,
          'page.url': window.location.href,
          'page.title': document.title,
          'page.referrer': document.referrer,
          'navigation.type': getNavigationType(),
          ...attributes,
        },
      },
      async () => {
        // Page view tracking span
      },
    )
  }
}

/**
 * Track an error
 * Use this to capture errors with full context
 */
export async function trackError(error: Error, context?: Record<string, any>): Promise<void> {
  const tracer = await getWebTracer()

  if (tracer) {
    tracer.withSpan(
      {
        name: 'web_error',
        attributes: {
          'error.type': 'web_error',
          'error.source': 'manual',
          'page.url': window.location.href,
          'page.title': document.title,
          ...context,
        },
      },
      async (span: any) => {
        tracer.recordError(span, error)
      },
    )
  }
}

/**
 * Add custom attributes to the current span
 * Use this to add business-specific context to traces
 */
export async function addTraceAttributes(attributes: Record<string, any>): Promise<void> {
  // Early return if tracing is disabled
  if (!tracingConfig.enabled) {
    return
  }

  const tracer = await getWebTracer()

  if (tracer) {
    tracer.addAttributes(attributes)
  }
}

/**
 * Get current trace ID for logging correlation
 * Use this to add trace ID to log messages for correlation
 */
export async function getCurrentTraceId(): Promise<string | undefined> {
  const tracer = await getWebTracer()
  return tracer?.getCurrentTraceId()
}

/**
 * Get current span ID for logging correlation
 */
export async function getCurrentSpanId(): Promise<string | undefined> {
  const tracer = await getWebTracer()
  return tracer?.getCurrentSpanId()
}

/**
 * Get trace context headers for API calls
 * Use this when making HTTP calls to propagate traces
 */
export async function getTraceContextHeaders(): Promise<Record<string, string>> {
  const tracer = await getWebTracer()
  return tracer?.getTraceContext() || {}
}

/**
 * Get navigation type for performance tracking
 */
function getNavigationType(): string {
  if ('navigation' in performance) {
    const navigation = (performance as any).navigation
    switch (navigation.type) {
      case 0:
        return 'navigate'
      case 1:
        return 'reload'
      case 2:
        return 'back_forward'
      default:
        return 'unknown'
    }
  }
  return 'unknown'
}

/**
 * Shutdown tracing
 * Call this when the application is being unloaded
 */
export async function shutdownBrowserTracing(): Promise<void> {
  try {
    const { shutdownBrowserTracing: shutdownLib } = await import('@c-cam/tracing-browser')
    await shutdownLib()
    console.log('Browser tracing shutdown successfully')
  } catch (error) {
    console.error('Failed to shutdown browser tracing', error)
  }
}

// Set up automatic shutdown on page unload
window.addEventListener('beforeunload', () => {
  shutdownBrowserTracing()
})
