import { tracingConfig } from './configs/tracing.config'
import { addTraceAttributes } from './tracing.js'

const reportWebVitals = (onPerfEntry?: (metric: any) => void) => {
  const reportToTracing = (metric: any) => {
    // Only add web vitals to tracing if tracing is enabled
    if (tracingConfig.enabled) {
      addTraceAttributes({
        [`web_vital.${metric.name}`]: metric.value,
        [`web_vital.${metric.name}.rating`]: metric.rating,
        [`web_vital.${metric.name}.delta`]: metric.delta,
        [`web_vital.${metric.name}.id`]: metric.id,
      })
    }

    // Call the original callback if provided
    if (onPerfEntry && onPerfEntry instanceof Function) {
      onPerfEntry(metric)
    }
  }

  import('web-vitals')
    .then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {
      onCLS(reportToTracing)
      onINP(reportToTracing)
      onFCP(reportToTracing)
      onLCP(reportToTracing)
      onTTFB(reportToTracing)
    })
    .catch(console.error)
}

export default reportWebVitals
