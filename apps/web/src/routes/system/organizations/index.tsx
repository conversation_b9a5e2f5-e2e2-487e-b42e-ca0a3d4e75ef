import ProtectedLayout from '@/components/layout/protected.layout'
import { AddUserDialog } from '@/components/shared/add-user-dialog'
import { ChangeStatusDialog } from '@/components/shared/change-status-dialog'
import { ChangeUnitDialog } from '@/components/shared/change-unit-dialog'
import { DataTable } from '@/components/shared/data-table'
import { UserDetailDrawer } from '@/components/shared/member-detail-drawer'

import {
  ExportIcon,
  FilterIcon,
  ImportIcon,
  MenuDotsIcon,
  PlusIcon,
} from '@/components/shared/icons'
import type { SelectionAction } from '@/components/shared/selection-bar'
import { SelectionBar } from '@/components/shared/selection-bar'
import type { StatusOption } from '@/components/shared/status-combobox'
import { ToggleStatusDialog } from '@/components/shared/toggle-status-dialog'
import UnitSelector from '@/components/shared/unit-selector/UnitSelector'
import { UpdateAccountDialog } from '@/components/shared/update-account-dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useSelectedTenant } from '@/hooks/use-selected-tenant'
import { Unit } from '@/hooks/use-unit-query'
import {
  useMembersByTenantQuery,
  useMembersByUnitQuery,
  UserStatus,
  type Member as APIMember,
} from '@/hooks/use-user-query'
import { useTableSelection } from '@/shared/hooks/use-table-selection'
import { createFileRoute } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { Building, ToggleLeft } from 'lucide-react'
import { useEffect, useMemo, useState } from 'react'

export const Route = createFileRoute('/system/organizations/')({
  component: RouteComponent,
})

// Interface cho user (phù hợp với API response)
interface User {
  id: string
  unit_id: string
  member_role_id?: string
  code: string
  name: string
  email?: string
  phone?: string
  dob?: string
  gender?: string
  username: string
  status: 'active' | 'inactive'
  created_by: string
  createdAt: string
  updatedAt: string
  avatar_id?: string
  face_id?: string
  // Populated fields from backend
  tenant?: {
    id: string
    name: string
  }
  unit?: {
    id: string
    name: string
  }
}

// Convert API User to local User type
const convertApiUserToUser = (apiUser: APIMember): User => {
  // Handle status conversion - if status is null/undefined, default to inactive
  // Only show as active if explicitly set to ACTIVE
  const getStatus = (apiStatus?: string): 'active' | 'inactive' => {
    if (!apiStatus) return 'inactive' // null/undefined status defaults to inactive
    return apiStatus === UserStatus.ACTIVE ? 'active' : 'inactive'
  }

  return {
    id: apiUser._id || apiUser.id,
    unit_id: apiUser.unit_id || '',
    member_role_id: apiUser.member_role_id,
    code: apiUser.code || '',
    name:
      apiUser.name ||
      apiUser.full_name ||
      `${apiUser.first_name || ''} ${apiUser.last_name || ''}`.trim() ||
      apiUser.username,
    email: apiUser.email,
    phone: apiUser.phone,
    dob: apiUser.dob
      ? typeof apiUser.dob === 'string'
        ? apiUser.dob
        : apiUser.dob.toISOString()
      : undefined,
    gender: apiUser.gender,
    username: apiUser.username || '',
    status: getStatus(apiUser.status),
    created_by: apiUser.created_by || '',
    createdAt: apiUser.created_at
      ? typeof apiUser.created_at === 'string'
        ? apiUser.created_at
        : apiUser.created_at.toISOString()
      : '',
    updatedAt: apiUser.updated_at
      ? typeof apiUser.updated_at === 'string'
        ? apiUser.updated_at
        : apiUser.updated_at.toISOString()
      : '',
    avatar_id: apiUser.avatar_id,
    face_id: apiUser.face_id,
    // Add populated fields from backend
    tenant: apiUser.tenant,
    unit: apiUser.unit,
  }
}

function RouteComponent() {
  const [selectedUnit, setSelectedUnit] = useState<Unit | undefined>()
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [tempStatusFilter, setTempStatusFilter] = useState<string>('all')

  // Pagination state
  const [pageSize, setPageSize] = useState(10)
  const [pageIndex, setPageIndex] = useState(0)

  // Get selected tenant from localStorage
  const { selectedTenant, selectedTenantId } = useSelectedTenant()

  // Fetch members data by selected unit ID (when unit is selected)
  const { data: unitMembersData, refetch: refetchUnitMembers } =
    useMembersByUnitQuery(
      selectedUnit?.id || '',
      !!selectedUnit?.id,
      pageSize,
      pageIndex,
    )

  // Fetch all members data by tenant (when no unit is selected)
  // Note: tenantId is now automatically retrieved from access token
  const { data: tenantMembersData, refetch: refetchTenantMembers } =
    useMembersByTenantQuery(
      !selectedUnit?.id, // enabled when no unit is selected
      pageSize,
      pageIndex,
    )

  // Use unit members if unit is selected, otherwise use tenant members
  const membersData = selectedUnit?.id ? unitMembersData : tenantMembersData
  const refetchMembers = selectedUnit?.id
    ? refetchUnitMembers
    : refetchTenantMembers
  const [changeUnitDialogOpen, setChangeUnitDialogOpen] = useState(false)
  const [changeUnitSingleDialogOpen, setChangeUnitSingleDialogOpen] =
    useState(false)
  const [singleUserForUnitChange, setSingleUserForUnitChange] =
    useState<User | null>(null)
  const [changeStatusDialogOpen, setChangeStatusDialogOpen] = useState(false)
  const [userDetailDrawerOpen, setUserDetailDrawerOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false)
  const [editUserData, setEditUserData] = useState<any>(null)
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create')
  const [updateAccountDialogOpen, setUpdateAccountDialogOpen] = useState(false)
  const [userForAccountUpdate, setUserForAccountUpdate] = useState<User | null>(
    null,
  )
  const [toggleStatusDialogOpen, setToggleStatusDialogOpen] = useState(false)
  const [userForStatusToggle, setUserForStatusToggle] = useState<User | null>(
    null,
  )

  // Use the table selection hook with persistent selection across pages
  const {
    selectedCount,
    hasSelection,
    selectedIds,
    handleSelectionChange,
    getRowSelectionState,
    clearSelection,
  } = useTableSelection<User>({
    getRowId: (user) => user.id, // Use user.id as unique identifier
    persistAcrossPages: true, // Enable persistent selection across pages
  })

  const handleSelectUnit = (unit?: Unit) => {
    setSelectedUnit(unit)
    console.log('Selected unit:', unit)
    // Clear selection when unit changes
    clearSelection()
  }

  // Effect to reset selectedUnit and clear selection when tenant changes
  useEffect(() => {
    console.log('Tenant changed, resetting selectedUnit:', {
      selectedTenantId,
      selectedTenant: selectedTenant?.name,
      currentSelectedUnit: selectedUnit?.name,
    })
    setSelectedUnit(undefined)
    // Clear checkbox selections when tenant changes
    clearSelection()
  }, [selectedTenantId, clearSelection])

  // Debug effect to track query states
  useEffect(() => {
    console.log('Query states:', {
      selectedTenantId,
      selectedUnitId: selectedUnit?.id,
      unitQueryEnabled: !!selectedUnit?.id,
      tenantMembersQueryEnabled: !!selectedTenantId && !selectedUnit?.id,
      unitMembersCount: unitMembersData?.users?.length || 0,
      tenantMembersCount: tenantMembersData?.users?.length || 0,
    })
  }, [selectedTenantId, selectedUnit?.id, unitMembersData, tenantMembersData])

  const handleTempStatusFilterSelect = (value: string) => {
    setTempStatusFilter(value)
  }

  const applyFilters = () => {
    setStatusFilter(tempStatusFilter)
  }

  const clearFilters = () => {
    setTempStatusFilter('all')
    setStatusFilter('all')
  }

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'active':
        return 'Hoạt động'
      case 'inactive':
        return 'Ngưng hoạt động'
      default:
        return 'Tất cả'
    }
  }

  const filterButtonText =
    statusFilter === 'all'
      ? 'Bộ lọc'
      : `Bộ lọc (${getStatusDisplay(statusFilter)})`

  // Handle change unit confirmation
  const handleChangeUnitConfirm = (newUnit: Unit, userIds: string[]) => {
    console.log('Changing unit for users:', userIds, 'to unit:', newUnit)
    // Selection will be cleared automatically after successful API call
    // Here you would call your API to update the users' units
    // After successful update, you might want to refresh the data or show a success message
  }

  // Handle single user unit change confirmation
  const handleChangeUnitSingleConfirm = (newUnit: Unit, userIds: string[]) => {
    if (singleUserForUnitChange) {
      console.log(
        'Changing unit for user:',
        singleUserForUnitChange.name,
        'to unit:',
        newUnit,
        'userIds:',
        userIds,
      )
      // Here you would call your API to update the single user's unit
      // After successful update, refresh the data or show a success message
    }
    setChangeUnitSingleDialogOpen(false)
    setSingleUserForUnitChange(null)
  }

  // Handle change status confirmation
  const handleChangeStatusConfirm = (newStatus: StatusOption) => {
    console.log(
      'Changing status for users:',
      selectedIds,
      'to status:',
      newStatus,
    )
    // Selection will be cleared automatically after successful API call
    // Here you would call your API to update the users' status
    // After successful update, you might want to refresh the data or show a success message
  }

  // Handle individual member actions (placeholder - will be replaced below)

  const handleChangeUnitSingle = (user: User) => {
    setSingleUserForUnitChange(user)
    setChangeUnitSingleDialogOpen(true)
  }

  const handleUpdateAccount = (user: User) => {
    setUserForAccountUpdate(user)
    setUpdateAccountDialogOpen(true)
  }

  // Handle update account confirmation
  const handleUpdateAccountConfirm = (data: {
    userId: string
    newPassword: string
  }) => {
    console.log(
      'Updating account password for user:',
      data.userId,
      'with new password',
    )
    // TODO: Call API to update user password
    // After successful update, show success message
    setUpdateAccountDialogOpen(false)
    setUserForAccountUpdate(null)
  }

  const handleToggleStatus = (user: User) => {
    setUserForStatusToggle(user)
    setToggleStatusDialogOpen(true)
  }

  // Handle toggle status confirmation
  const handleToggleStatusConfirm = (
    user: User,
    newStatus: 'active' | 'inactive',
  ) => {
    console.log(
      'Toggling status for user:',
      user.name,
      'from',
      user.status,
      'to',
      newStatus,
    )
    // TODO: Call API to update user status
    // After successful update, refresh user data and show success message
    setToggleStatusDialogOpen(false)
    setUserForStatusToggle(null)
  }

  // Handle user detail view
  const handleViewUserDetail = (user: User) => {
    setSelectedUser(user)
    setUserDetailDrawerOpen(true)
  }

  const handleCloseUserDetail = () => {
    setUserDetailDrawerOpen(false)
    setSelectedUser(null)
  }

  // Unit management is now handled by UnitSelector internally

  // Handle add member
  const handleAddMember = () => {
    setDialogMode('create')
    setEditUserData(null)
    setAddUserDialogOpen(true)
  }

  // Handle edit user
  const handleEditUser = (user: User) => {
    // Convert User to UserData format
    const userData = {
      id: user.id,
      code: user.code || '',
      name: user.name,
      email: user.email || '',
      phone: user.phone || '', // Use phone from API response
      organizationId: 'org-1', // Default organization
      unitId: user.unit_id || '',
      unitName: '', // Will be populated by form
      role: user.member_role_id || '', // Use member_role_id from API response
      birthDate: user.dob ? new Date(user.dob).toISOString().split('T')[0] : '', // Convert Date to string
      gender: user.gender || '', // Use gender from API response
      username: user.username || '', // Use username from API response
      password: '', // Don't pre-fill password for security
    }

    setDialogMode('edit')
    setEditUserData(userData)
    setAddUserDialogOpen(true)
  }

  const handleAddMemberConfirm = (
    userData: {
      id?: string
      code: string
      name: string
      email: string
      phone: string
      organizationId: string
      unitId: string
      unitName: string
      role: string
      birthDate?: string
      gender?: string
      username: string
      password: string
    },
    isEdit?: boolean,
  ) => {
    // The actual API call is handled by AddMemberDialog component
    // This function is called after successful operation to refresh data
    console.log(
      isEdit ? 'User updated successfully' : 'User created successfully',
      userData,
    )

    // Refresh members data after successful operation
    refetchMembers()

    // Close dialog
    setAddUserDialogOpen(false)
    setEditUserData(null)
  }

  // Handle page size change - update state and reset to first page
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize)
    setPageIndex(0) // Reset to first page when page size changes
  }

  // Handle page change - update page index
  const handlePageChange = (newPageIndex: number) => {
    setPageIndex(newPageIndex)
  }

  // Define selection actions
  const selectionActions: SelectionAction[] = [
    {
      key: 'change-department',
      label: 'Đổi đơn vị',
      icon: <Building className="h-4 w-4" />,
      onClick: () => {
        setChangeUnitDialogOpen(true)
      },
    },
    {
      key: 'change-status',
      label: 'Chuyển trạng thái',
      icon: <ToggleLeft className="h-4 w-4" />,
      onClick: () => {
        setChangeStatusDialogOpen(true)
      },
    },
  ]

  // Convert API users to local User type and filter by status
  const filteredUsers = useMemo(() => {
    const apiUsers = membersData?.users || []

    // Convert API users to local User type
    let converted = apiUsers.map((apiUser: APIMember) =>
      convertApiUserToUser(apiUser),
    )

    // Filter by status if not 'all'
    if (statusFilter !== 'all') {
      converted = converted.filter((user: User) => user.status === statusFilter)
    }

    return converted
  }, [membersData, statusFilter])

  // Định nghĩa các cột cho bảng
  const columns: ColumnDef<User>[] = useMemo(
    () => [
      {
        accessorKey: 'member',
        header: 'Thành viên',
        cell: ({ row }) => (
          <div>
            <div className="font-medium text-sm">{row.original.name}</div>
            <div className="text-xs text-gray-500">{row.original.code}</div>
          </div>
        ),
      },
      {
        accessorKey: 'unit_id',
        header: 'Đơn vị',
        cell: ({ row }) => (
          <span className="text-sm">{row.original.unit?.name ?? '-'}</span>
        ),
      },
      {
        accessorKey: 'email',
        header: 'Email',
        cell: ({ row }) => (
          <span className="text-sm">{row.original.email}</span>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Trạng thái',
        cell: ({ row }) => (
          <span
            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              row.original.status === 'active'
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
          >
            {row.original.status === 'active' ? 'Hoạt động' : 'Ngưng hoạt động'}
          </span>
        ),
      },
      {
        id: 'actions',
        header: 'Thao tác',
        cell: ({ row }) => (
          <div className="flex items-center gap-2">
            <button
              onClick={() => handleViewUserDetail(row.original)}
              className="text-[#008fd3] hover:text-[#007bb8] text-sm font-medium cursor-pointer"
            >
              Chi tiết
            </button>

            <Popover>
              <PopoverTrigger asChild>
                <button
                  className="p-1 hover:bg-gray-200 rounded transition-colors cursor-pointer"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MenuDotsIcon className="w-3 h-3" />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-56 p-2" align="end">
                <div className="space-y-1">
                  <button
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                    onClick={() => handleEditUser(row.original)}
                  >
                    Chỉnh sửa
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                    onClick={() => handleChangeUnitSingle(row.original)}
                  >
                    Đổi đơn vị
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                    onClick={() => handleUpdateAccount(row.original)}
                  >
                    Cập nhật tài khoản
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded transition-colors cursor-pointer"
                    onClick={() => handleToggleStatus(row.original)}
                  >
                    {row.original.status === 'active'
                      ? 'Ngưng hoạt động'
                      : 'Hoạt động'}
                  </button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        ),
      },
    ],
    [],
  )

  return (
    <ProtectedLayout>
      <div className="flex h-screen bg-white">
        {/* Sidebar */}
        <div className="w-[260px] px-4 py-5 bg-white border-r border-[#e9eaf2]">
          <UnitSelector
            key={selectedTenant?.id || 'no-tenant'} // Force re-mount when tenant changes
            onSelectUnit={handleSelectUnit}
            selectedUnitId={selectedUnit?.id}
            searchPlaceholder="Theo tên đơn vị"
            className="w-full"
            useTenantData={true}
            showAddButton={true}
          />
        </div>

        {/* Main content */}
        <div className="flex-1 px-6 py-5">
          <div className="flex justify-between items-center mb-3">
            <h1 className="text-xl font-semibold text-[#1f2329]">
              {selectedUnit
                ? selectedUnit.name
                : selectedTenant?.name || 'Tổ chức'}
            </h1>
            <div className="flex gap-2">
              <button className="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-[#1f2329] bg-white border border-[#d0d3d6] rounded-md hover:bg-gray-50 cursor-pointer">
                <ExportIcon />
                Xuất dữ liệu
              </button>
              <button className="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-[#1f2329] bg-white border border-[#d0d3d6] rounded-md hover:bg-gray-50 cursor-pointer">
                <ImportIcon />
                Nhập dữ liệu
              </button>
              <button
                onClick={handleAddMember}
                className="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-white bg-[#008fd3] rounded-md hover:bg-[#007bb8] cursor-pointer"
              >
                <PlusIcon fillColor="white" />
                Thêm thành viên
              </button>
            </div>
          </div>

          {/* Search and filters */}
          <div className="flex gap-2 mb-4">
            <div className="w-[250px]">
              <Input
                type="text"
                placeholder="Theo tên hoặc email"
                className="text-xs"
              />
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 border-[#d0d3d6] text-[#1f2329] cursor-pointer"
                >
                  <FilterIcon />
                  <span className="text-xs font-medium">
                    {filterButtonText}
                  </span>
                </Button>
              </PopoverTrigger>
              <PopoverContent align="start" className="w-64 p-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label
                      htmlFor="user-status"
                      className="text-sm font-medium text-[#1f2329]"
                    >
                      Trạng thái người dùng:
                    </Label>
                    <Select
                      value={tempStatusFilter || 'all'}
                      onValueChange={handleTempStatusFilterSelect}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Chọn trạng thái" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tất cả</SelectItem>
                        <SelectItem value="active">Hoạt động</SelectItem>
                        <SelectItem value="inactive">
                          Ngưng hoạt động
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Filter Actions */}
                  <div className="flex justify-end">
                    <div className="flex items-center gap-2 pt-2 w-[130px]">
                      <Button
                        onClick={clearFilters}
                        variant="outline"
                        className="flex-1 border-[#d0d3d6] text-[#1f2329] text-xs font-medium"
                        size="sm"
                      >
                        Hủy bỏ
                      </Button>
                      <Button
                        onClick={applyFilters}
                        className="flex-1 bg-[#008fd3] hover:bg-[#008fd3]/90 text-white text-xs font-medium"
                        size="sm"
                      >
                        Lọc
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Selection info */}
          {hasSelection && (
            <SelectionBar
              selectedCount={selectedCount}
              actions={selectionActions}
              itemName="thành viên"
              className="mb-4"
            />
          )}

          {/* Content area */}
          <div className="bg-white rounded-lg">
            <DataTable
              columns={columns}
              data={filteredUsers}
              pageSize={pageSize}
              pageIndex={pageIndex}
              showPagination={true}
              sizeChanger={true}
              enableRowSelection={true}
              onRowSelectionChange={handleSelectionChange}
              rowSelection={getRowSelectionState(filteredUsers)}
              onPageSizeChange={handlePageSizeChange}
              onPageChange={handlePageChange}
              totalPages={membersData?.totalPages || 0}
              totalCount={membersData?.totalCount || 0}
              columnWidths={{
                select: 20,
                member: 200,
                unit_id: 150,
                email: 200,
                status: 120,
                actions: 100,
              }}
            />
          </div>
        </div>

        {/* Change Unit Dialog */}
        <ChangeUnitDialog
          open={changeUnitDialogOpen}
          onOpenChange={setChangeUnitDialogOpen}
          selectedCount={selectedCount}
          onConfirm={handleChangeUnitConfirm}
          userIds={selectedIds.map((id) => String(id))}
          selectedUnitId={selectedUnit?.id}
          clearSelection={clearSelection}
        />

        {/* Change Unit Dialog for Single Member */}
        <ChangeUnitDialog
          open={changeUnitSingleDialogOpen}
          onOpenChange={setChangeUnitSingleDialogOpen}
          selectedCount={1}
          onConfirm={handleChangeUnitSingleConfirm}
          userIds={singleUserForUnitChange ? [singleUserForUnitChange.id] : []}
          singleUser={singleUserForUnitChange}
          selectedUnitId={selectedUnit?.id}
          clearSelection={clearSelection}
        />

        {/* Change Status Dialog */}
        <ChangeStatusDialog
          open={changeStatusDialogOpen}
          onOpenChange={setChangeStatusDialogOpen}
          selectedCount={selectedCount}
          onConfirm={handleChangeStatusConfirm}
          userIds={selectedIds.map((id) => String(id))}
          clearSelection={clearSelection}
        />

        {/* User Detail Drawer */}
        <UserDetailDrawer
          open={userDetailDrawerOpen}
          onClose={handleCloseUserDetail}
          user={selectedUser}
          onEditUser={handleEditUser}
          onChangeUnit={handleChangeUnitSingle}
          onUpdateAccount={handleUpdateAccount}
          onToggleStatus={handleToggleStatus}
        />

        {/* Add User Dialog */}
        <AddUserDialog
          open={addUserDialogOpen}
          onOpenChange={setAddUserDialogOpen}
          onConfirm={handleAddMemberConfirm}
          editUser={editUserData}
          mode={dialogMode}
        />

        {/* Update Account Dialog */}
        <UpdateAccountDialog
          open={updateAccountDialogOpen}
          onOpenChange={setUpdateAccountDialogOpen}
          member={userForAccountUpdate}
          onConfirm={handleUpdateAccountConfirm}
        />

        {/* Toggle Status Dialog */}
        <ToggleStatusDialog
          open={toggleStatusDialogOpen}
          onOpenChange={setToggleStatusDialogOpen}
          member={userForStatusToggle}
          onConfirm={handleToggleStatusConfirm}
        />
      </div>
    </ProtectedLayout>
  )
}
