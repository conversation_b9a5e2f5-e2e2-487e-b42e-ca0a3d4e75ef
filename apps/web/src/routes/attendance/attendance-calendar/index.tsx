import ProtectedLayout from '@/components/layout/protected.layout'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useSelectedTenant } from '@/hooks/use-selected-tenant'
import { createFileRoute } from '@tanstack/react-router'
import { CalendarDays, Download, Search, Users } from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/attendance/attendance-calendar/')({
  component: RouteComponent,
})

interface AttendanceRecord {
  id: string
  employeeId: string
  employeeName: string
  date: string
  checkIn: string
  checkOut: string
  workingHours: number
  status: 'present' | 'absent' | 'late' | 'early_leave'
  unit: string
}

// Mock data for demonstration
const mockAttendanceData: AttendanceRecord[] = [
  {
    id: '1',
    employeeId: 'EMP001',
    employeeName: 'Nguyễn Văn A',
    date: '2024-01-15',
    checkIn: '08:00',
    checkOut: '17:30',
    workingHours: 8.5,
    status: 'present',
    unit: 'Phòng IT',
  },
  {
    id: '2',
    employeeId: 'EMP002',
    employeeName: 'Trần Thị B',
    date: '2024-01-15',
    checkIn: '08:15',
    checkOut: '17:30',
    workingHours: 8.25,
    status: 'late',
    unit: 'Phòng HR',
  },
  {
    id: '3',
    employeeId: 'EMP003',
    employeeName: 'Lê Văn C',
    date: '2024-01-15',
    checkIn: '-',
    checkOut: '-',
    workingHours: 0,
    status: 'absent',
    unit: 'Phòng Kế toán',
  },
]

function RouteComponent() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [unitFilter, setUnitFilter] = useState<string>('all')

  const { selectedTenant } = useSelectedTenant()

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'present':
        return { label: 'Có mặt', color: 'bg-green-100 text-green-800' }
      case 'absent':
        return { label: 'Vắng mặt', color: 'bg-red-100 text-red-800' }
      case 'late':
        return { label: 'Đi muộn', color: 'bg-yellow-100 text-yellow-800' }
      case 'early_leave':
        return { label: 'Về sớm', color: 'bg-orange-100 text-orange-800' }
      default:
        return { label: 'Không xác định', color: 'bg-gray-100 text-gray-800' }
    }
  }

  const filteredData = mockAttendanceData.filter((record) => {
    const matchesSearch =
      record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus =
      statusFilter === 'all' || record.status === statusFilter
    const matchesUnit = unitFilter === 'all' || record.unit === unitFilter

    return matchesSearch && matchesStatus && matchesUnit
  })

  const stats = {
    total: mockAttendanceData.length,
    present: mockAttendanceData.filter((r) => r.status === 'present').length,
    absent: mockAttendanceData.filter((r) => r.status === 'absent').length,
    late: mockAttendanceData.filter((r) => r.status === 'late').length,
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col justify-start items-start w-[1200px] h-[822px] gap-4 px-6 py-5 bg-white">
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden gap-4">
          <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 h-10">
            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
              <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[39px] border-t-0 border-r-0 border-b border-l-0 border-[#008fd3]">
                <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-[39px] px-2">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                    <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#008fd3]">
                      Của tôi
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-[39px] px-2">
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                  <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#73787e]">
                    Của tổ chức
                  </p>
                </div>
              </div>
            </div>
            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-8 relative gap-2 px-2 rounded-md bg-white border border-[#d0d3d6]">
              <svg
                width={18}
                height={18}
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="flex-grow-0 flex-shrink-0 w-[18px] h-[18px] relative"
                preserveAspectRatio="none"
              >
                <path
                  d="M13.875 12C14.2543 12.3686 15.75 13.3498 15.75 13.875M13.875 15.75C14.2543 15.3814 15.75 14.4002 15.75 13.875M15.75 13.875L9.75 13.875"
                  stroke="#1F2329"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M8.25 16.5H8.04546C5.59955 16.5 4.3766 16.5 3.5273 15.9016C3.28397 15.7302 3.06794 15.5269 2.88578 15.2978C2.25 14.4985 2.25 13.3475 2.25 11.0455V9.13636C2.25 6.91398 2.25 5.8028 2.6017 4.91531C3.16711 3.48857 4.36285 2.36316 5.87877 1.83101C6.82172 1.5 8.00236 1.5 10.3636 1.5C11.7129 1.5 12.3876 1.5 12.9264 1.68915C13.7927 1.99324 14.4759 2.63632 14.799 3.45161C15 3.95874 15 4.59371 15 5.86364V9.75"
                  stroke="#1F2329"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M2.25 9C2.25 7.61929 3.36929 6.5 4.75 6.5C5.24934 6.5 5.83803 6.58749 6.32352 6.45741C6.75489 6.34182 7.09182 6.00489 7.20741 5.57352C7.3375 5.08803 7.25 4.49934 7.25 4C7.25 2.61929 8.36929 1.5 9.75 1.5"
                  stroke="#1F2329"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#1f2329]">
                Xuất dữ liệu
              </p>
            </div>
          </div>
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
            <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative gap-4 p-px rounded-xl">
              <div className="flex-grow-0 flex-shrink-0 w-[58px] h-1 rounded-xl bg-[#d9d9d9]" />
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                <div className="flex flex-col justify-start items-start flex-grow overflow-hidden gap-2.5 px-[126px] py-3 rounded-xl bg-white border border-[#dadde1]">
                  <div className="flex flex-col justify-center items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                    <p className="flex-grow-0 flex-shrink-0 text-xl font-bold text-left text-[#3ab67b]">
                      22/23
                    </p>
                    <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#73787e]">
                      Ngày công
                    </p>
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start flex-grow overflow-hidden gap-2.5 px-[107px] py-3 rounded-xl bg-white border border-[#dadde1]">
                  <div className="flex flex-col justify-center items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                    <p className="flex-grow-0 flex-shrink-0 text-xl font-bold text-left text-[#fa8c16]">
                      04
                    </p>
                    <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#73787e]">
                      Đi muộn/Về sớm
                    </p>
                  </div>
                </div>
                <div className="flex flex-col justify-center items-center flex-grow overflow-hidden gap-2.5 px-[89px] py-3 rounded-xl bg-white border border-[#dadde1]">
                  <div className="flex flex-col justify-center items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                    <p className="flex-grow-0 flex-shrink-0 text-xl font-bold text-left text-[#e94040]">
                      01
                    </p>
                    <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#73787e]">
                      Không chấm công
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden rounded-xl border border-[#dadde1]">
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 h-px relative gap-2.5">
                  <div className="self-stretch flex-grow-0 flex-shrink-0 h-px bg-[#eceef1]" />
                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 gap-3 py-2">
                  <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 relative p-2 rounded">
                    <svg
                      width={17}
                      height={16}
                      viewBox="0 0 17 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                      preserveAspectRatio="none"
                    >
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M10.3048 3.5282C10.4298 3.65322 10.5 3.82276 10.5 3.99953C10.5 4.17631 10.4298 4.34585 10.3048 4.47087L6.77618 7.99953L10.3048 11.5282C10.4263 11.6539 10.4935 11.8223 10.492 11.9971C10.4904 12.1719 10.4203 12.3391 10.2967 12.4627C10.1731 12.5864 10.0059 12.6565 9.83112 12.658C9.65632 12.6595 9.48792 12.5923 9.36218 12.4709L5.36218 8.47087C5.2372 8.34585 5.16699 8.17631 5.16699 7.99953C5.16699 7.82276 5.2372 7.65322 5.36218 7.5282L9.36218 3.5282C9.4872 3.40322 9.65674 3.33301 9.83352 3.33301C10.0103 3.33301 10.1798 3.40322 10.3048 3.5282Z"
                        fill="#73787E"
                      />
                    </svg>
                  </div>
                  <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 px-3 rounded-lg border border-[#dadde1]">
                    <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative overflow-hidden">
                      <p className="flex-grow-0 flex-shrink-0 text-[13.890625px] text-center text-[#1f2329]">
                        Hôm nay
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 px-3 rounded">
                    <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#1f2329]">
                        01/07/2024 - 31/07/2024
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 relative p-2 rounded">
                    <svg
                      width={17}
                      height={16}
                      viewBox="0 0 17 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                      preserveAspectRatio="none"
                    >
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M6.69519 3.5282C6.82021 3.40322 6.98975 3.33301 7.16652 3.33301C7.3433 3.33301 7.51284 3.40322 7.63786 3.5282L11.6379 7.5282C11.7628 7.65322 11.833 7.82276 11.833 7.99953C11.833 8.17631 11.7628 8.34585 11.6379 8.47087L7.63786 12.4709C7.51212 12.5923 7.34372 12.6595 7.16892 12.658C6.99413 12.6565 6.82692 12.5864 6.70331 12.4627C6.57971 12.3391 6.50959 12.1719 6.50808 11.9971C6.50656 11.8223 6.57375 11.6539 6.69519 11.5282L10.2239 7.99953L6.69519 4.47087C6.57021 4.34585 6.5 4.17631 6.5 3.99953C6.5 3.82276 6.57021 3.65322 6.69519 3.5282Z"
                        fill="#73787E"
                      />
                    </svg>
                  </div>
                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 gap-10 py-2">
                  <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2">
                    <div className="flex-grow-0 flex-shrink-0 w-3 h-3 rounded-md bg-[#3ab67b]" />
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#1f2329]">
                        Đúng giờ
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2">
                    <div className="flex-grow-0 flex-shrink-0 w-3 h-3 rounded-md bg-[#fa8c16]" />
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                      <p className="flex-grow-0 flex-shrink-0 text-[13.890625px] font-medium text-left text-[#1f2329]">
                        Đi muộn / Về sớm
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2">
                    <div className="flex-grow-0 flex-shrink-0 w-3 h-3 rounded-md bg-[#e94040]" />
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#1f2329]">
                        Không chấm công
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2">
                    <div className="flex-grow-0 flex-shrink-0 w-3 h-3 rounded-md bg-[#722ed1]" />
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#1f2329]">
                        Lịch nghỉ
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2">
                    <div className="flex-grow-0 flex-shrink-0 w-3 h-3 rounded-md bg-white border border-[#9a9ea5]" />
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                      <p className="flex-grow-0 flex-shrink-0 text-[13.890625px] font-medium text-left text-[#1f2329]">
                        Chưa được phân ca
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 bg-white">
                  <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 py-2 bg-[#f5f7f9]">
                    <div className="flex justify-center items-center flex-grow relative gap-1 px-3 py-1">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#18202a]">
                        T2
                      </p>
                    </div>
                    <div className="flex justify-center items-center flex-grow relative gap-1 px-3 py-1">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#18202a]">
                        T3
                      </p>
                    </div>
                    <div className="flex justify-center items-center flex-grow relative gap-1 px-3 py-1">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#18202a]">
                        T4
                      </p>
                    </div>
                    <div className="flex justify-center items-center flex-grow relative gap-1 px-3 py-1">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#18202a]">
                        T5
                      </p>
                    </div>
                    <div className="flex justify-center items-center flex-grow relative gap-1 px-3 py-1">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#18202a]">
                        Th6{' '}
                      </p>
                    </div>
                    <div className="flex justify-center items-center flex-grow relative gap-1 px-3 py-1">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#18202a]">
                        Th7
                      </p>
                    </div>
                    <div className="flex justify-center items-center flex-grow relative gap-1 px-3 py-1">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#18202a]">
                        CN
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 h-px relative gap-2.5">
                    <div className="self-stretch flex-grow-0 flex-shrink-0 h-px bg-[#eceef1]" />
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 bg-white">
                  <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative py-2">
                    <div className="flex flex-col justify-center items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          1
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#ffeecf]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:40 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#ffa940]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center self-stretch flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          2
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:30 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          3
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          4
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                        <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-1 px-2.5 py-1">
                          <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#008fd3]">
                            +2 ca làm việc
                          </p>
                          <svg
                            width={17}
                            height={16}
                            viewBox="0 0 17 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M12.9713 6.19519C13.0963 6.32021 13.1665 6.48975 13.1665 6.66652C13.1665 6.8433 13.0963 7.01284 12.9713 7.13786L8.97131 11.1379C8.84629 11.2628 8.67676 11.333 8.49998 11.333C8.3232 11.333 8.15366 11.2628 8.02865 11.1379L4.02865 7.13786C3.90721 7.01212 3.84001 6.84372 3.84153 6.66892C3.84305 6.49413 3.91316 6.32692 4.03677 6.20331C4.16037 6.07971 4.32758 6.00959 4.50238 6.00808C4.67718 6.00656 4.84558 6.07375 4.97131 6.19519L8.49998 9.72386L12.0286 6.19519C12.1537 6.07021 12.3232 6 12.5 6C12.6768 6 12.8463 6.07021 12.9713 6.19519Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          5
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          6
                        </p>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          7
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 h-px relative gap-2.5">
                    <div className="self-stretch flex-grow-0 flex-shrink-0 h-px bg-[#eceef1]" />
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 bg-white">
                  <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative py-2">
                    <div className="flex flex-col justify-center items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          8
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#ffeecf]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:40 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#ffa940]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          9
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          10
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          11
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#ffdad6]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  --
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  --
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#f5222d]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          12
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          13
                        </p>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          14
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 h-px relative gap-2.5">
                    <div className="self-stretch flex-grow-0 flex-shrink-0 h-px bg-[#eceef1]" />
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 bg-white">
                  <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative py-2">
                    <div className="flex flex-col justify-center items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          15
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-center items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          16
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          17
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#ffeecf]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:40 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#ffa940]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          18
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          19
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          20
                        </p>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          21
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 h-px relative gap-2.5">
                    <div className="self-stretch flex-grow-0 flex-shrink-0 h-px bg-[#eceef1]" />
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 bg-white">
                  <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative py-2">
                    <div className="flex flex-col justify-center items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          22
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-center items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          23
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          24
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#ffeecf]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:34 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#ffa940]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          25
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          26
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          27
                        </p>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          28
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 h-px relative gap-2.5">
                    <div className="self-stretch flex-grow-0 flex-shrink-0 h-px bg-[#eceef1]" />
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 bg-white">
                  <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 relative py-2">
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          29
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center self-stretch flex-grow px-0.5 rounded-md bg-[#f7edff]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                Nghỉ lễ ...
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#722ed1]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          30
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center self-stretch flex-grow px-0.5 rounded-md bg-[#f7edff]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                Nghỉ lễ 30/4
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#722ed1]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2.5 px-1 py-[3px] rounded bg-[#3ab67b]">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-white">
                          31
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center flex-grow px-0.5 rounded-md bg-[#56ca76]/[0.16]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#3ab67b]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          1
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center self-stretch flex-grow px-0.5 rounded-md bg-[#f7edff]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                Nghỉ lễ 30/4
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-0 relative gap-2.5">
                            <div className="self-stretch flex-grow-0 flex-shrink-0 w-1 bg-[#722ed1]" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center self-stretch flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1 rounded-tl-lg rounded-tr-lg">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          2
                        </p>
                      </div>
                      <div className="flex flex-col justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-1">
                        <div className="flex flex-row-reverse justify-end items-center self-stretch flex-grow-0 flex-shrink-0 h-10 overflow-hidden rounded-md">
                          <div className="flex justify-center items-center self-stretch flex-grow px-0.5 rounded-md bg-[#f5f7f9]">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-1 p-1">
                              <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  08:30 AM
                                </span>
                                <br />
                                <span className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#18202a]">
                                  06:00 PM
                                </span>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center self-stretch flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1 rounded-tl-lg rounded-tr-lg">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          3
                        </p>
                      </div>
                    </div>
                    <div className="self-stretch flex-grow-0 flex-shrink-0 w-px rounded-[1px] bg-[#eceef1]" />
                    <div className="flex flex-col justify-start items-center flex-grow overflow-hidden gap-1 px-2 bg-white">
                      <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 w-7 relative gap-2.5 p-1">
                        <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-center text-[#18202a]">
                          4
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 h-px relative gap-2.5">
                    <div className="self-stretch flex-grow-0 flex-shrink-0 h-px bg-[#eceef1]" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
