import { useState, useEffect } from 'react';
import ProtectedLayout from '@/components/layout/protected.layout';
import { createFileRoute } from '@tanstack/react-router';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { CreateShiftPopup } from '@/components/attendance/shifts/create-shift-popup';
import { useShift, CreateShiftRequest } from '@/hooks/use-shift';
import { toast } from 'sonner';

export const Route = createFileRoute('/attendance/shifts/')({
  component: RouteComponent,
})

function RouteComponent() {
  const [showCreatePopup, setShowCreatePopup] = useState(false);
  const [shifts, setShifts] = useState([]);
  const { loading, createShift, getShifts } = useShift();

  // Load shifts on component mount
  useEffect(() => {
    loadShifts();
  }, []);

  const loadShifts = async () => {
    try {
      const shiftsData = await getShifts();
      setShifts(shiftsData);
    } catch (error) {
      console.error('Failed to load shifts:', error);
    }
  };

  const handleCreateShift = async (data: CreateShiftRequest) => {
    try {
      await createShift(data);
      setShowCreatePopup(false);
      // Reload shifts after successful creation
      await loadShifts();
    } catch (error) {
      console.error('Failed to create shift:', error);
    }
  };

  return (
    <ProtectedLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Danh sách ca làm việc</h1>
            <p className="text-sm text-gray-600 mt-1">
              Quản lý các ca làm việc trong hệ thống
            </p>
          </div>
          <Button
            onClick={() => setShowCreatePopup(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Thêm ca làm việc
          </Button>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {shifts.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">
                  <svg
                    className="mx-auto h-12 w-12"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Chưa có ca làm việc nào
                </h3>
                <p className="text-gray-500 mb-4">
                  Bắt đầu bằng cách tạo ca làm việc đầu tiên
                </p>
                <Button
                  onClick={() => setShowCreatePopup(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Thêm ca làm việc
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Shifts list will be implemented here */}
                <p>Danh sách ca làm việc ({shifts.length} ca)</p>
                {/* TODO: Implement shifts table/list */}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Create Shift Popup */}
      <CreateShiftPopup
        open={showCreatePopup}
        onClose={() => setShowCreatePopup(false)}
        onSubmit={handleCreateShift}
        loading={loading}
      />
    </ProtectedLayout>
  )
}
