import ProtectedLayout from '@/components/layout/protected.layout'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { createFileRoute, Link } from '@tanstack/react-router'
import { ArrowLeft, Save, Users, Calendar } from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/attendance/shift-assignment/create/')({
  component: RouteComponent,
})

function RouteComponent() {
  const [formData, setFormData] = useState({
    employeeId: '',
    shiftId: '',
    date: '',
    startTime: '',
    endTime: '',
    notes: '',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Creating shift assignment:', formData)
    // Handle form submission here
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col justify-start items-start w-[1200px] h-[1137px] gap-4 px-6 py-5 bg-[#f5f6f7]">
        <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
          <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1">
            <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#8f959e]">
              Danh sách phân ca
            </p>
            <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#67718e]">
              /
            </p>
            <p className="flex-grow-0 flex-shrink-0 text-xs text-center text-[#1f2329]">
              Tạo mới phân ca
            </p>
          </div>
        </div>
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
            <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
              <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-6 py-5 rounded-lg bg-white">
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                  <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                    <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                      Thông tin cơ bản
                    </p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-4">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-[500px] gap-1">
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                      <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                        <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                          Tiêu đề
                        </p>
                        <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                          *
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                      <div className="flex justify-start items-center self-stretch flex-grow space-x-[-11px] px-[9px]">
                        <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                          <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                            <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#8f959e]">
                              Nhập tiêu đề phân ca
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-8">
                  <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[500px] gap-4">
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-[500px] gap-2">
                      <pre>
                        Failed to transform FRAME Container{'\n'}TypeError:
                        cannot read property 'className' of undefined
                      </pre>
                      <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0">
                        <pre>
                          Failed to transform FRAME Container{'\n'}TypeError:
                          cannot read property 'className' of undefined
                        </pre>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-[835px] gap-2">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-[500px] overflow-hidden">
                    <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                      <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                        Đối tượng áp dụng
                      </p>
                      <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                        *
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-[500px]">
                    <pre>
                      Failed to transform FRAME Container{'\n'}TypeError: cannot
                      read property 'className' of undefined
                    </pre>
                  </div>
                  <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-[835px]">
                    <div className="flex flex-col justify-center items-center flex-grow">
                      <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 p-4 rounded-lg bg-[#f5f6f7]">
                        <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2">
                          <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 w-[185px] overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                            <div className="flex justify-between items-center self-stretch flex-grow relative px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Phòng ban
                                  </p>
                                </div>
                              </div>
                              <svg
                                width={14}
                                height={15}
                                viewBox="0 0 14 15"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                preserveAspectRatio="none"
                              >
                                <path
                                  d="M3.5 5.75L6.29289 8.54289C6.62623 8.87623 6.79289 9.04289 7 9.04289C7.20711 9.04289 7.37377 8.87623 7.70711 8.54289L10.5 5.75"
                                  stroke="#646A73"
                                  stroke-width="1.5"
                                  stroke-linecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </div>
                          </div>
                          <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 w-[185px] overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                            <div className="flex justify-between items-center self-stretch flex-grow relative px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Bao gồm
                                  </p>
                                </div>
                              </div>
                              <svg
                                width={14}
                                height={15}
                                viewBox="0 0 14 15"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                preserveAspectRatio="none"
                              >
                                <path
                                  d="M3.5 5.75L6.29289 8.54289C6.62623 8.87623 6.79289 9.04289 7 9.04289C7.20711 9.04289 7.37377 8.87623 7.70711 8.54289L10.5 5.75"
                                  stroke="#646A73"
                                  stroke-width="1.5"
                                  stroke-linecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </div>
                          </div>
                          <div className="flex justify-start items-start flex-grow overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                            <div className="flex justify-between items-center self-stretch flex-grow relative px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#8f959e]">
                                    Chọn phòng ban
                                  </p>
                                </div>
                              </div>
                              <svg
                                width={14}
                                height={15}
                                viewBox="0 0 14 15"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                preserveAspectRatio="none"
                              >
                                <path
                                  d="M3.5 5.75L6.29289 8.54289C6.62623 8.87623 6.79289 9.04289 7 9.04289C7.20711 9.04289 7.37377 8.87623 7.70711 8.54289L10.5 5.75"
                                  stroke="#646A73"
                                  stroke-width="1.5"
                                  stroke-linecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 rounded-md">
                          <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                            <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                              <svg
                                width={14}
                                height={15}
                                viewBox="0 0 14 15"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                preserveAspectRatio="none"
                              >
                                <path
                                  d="M6.99984 1.66699C6.84513 1.66699 6.69675 1.72845 6.58736 1.83785C6.47796 1.94724 6.4165 2.09562 6.4165 2.25033V6.91699H1.74984C1.59513 6.91699 1.44675 6.97845 1.33736 7.08785C1.22796 7.19724 1.1665 7.34562 1.1665 7.50033C1.1665 7.65503 1.22796 7.80341 1.33736 7.9128C1.44675 8.0222 1.59513 8.08366 1.74984 8.08366H6.4165V12.7503C6.4165 12.905 6.47796 13.0534 6.58736 13.1628C6.69675 13.2722 6.84513 13.3337 6.99984 13.3337C7.15455 13.3337 7.30292 13.2722 7.41232 13.1628C7.52171 13.0534 7.58317 12.905 7.58317 12.7503V8.08366H12.2498C12.4045 8.08366 12.5529 8.0222 12.6623 7.9128C12.7717 7.80341 12.8332 7.65503 12.8332 7.50033C12.8332 7.34562 12.7717 7.19724 12.6623 7.08785C12.5529 6.97845 12.4045 6.91699 12.2498 6.91699H7.58317V2.25033C7.58317 2.09562 7.52171 1.94724 7.41232 1.83785C7.30292 1.72845 7.15455 1.66699 6.99984 1.66699Z"
                                  fill="#008FD3"
                                />
                              </svg>
                            </div>
                          </div>
                          <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#008fd3]">
                            Thêm điều kiện
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col justify-center items-start self-stretch flex-grow-0 flex-shrink-0 gap-4 px-6 py-5 rounded-lg bg-white">
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative">
                  <div className="flex-grow-0 flex-shrink-0 w-0.5 h-4 rounded-[1px] bg-[#008fd3]" />
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative pl-2">
                    <p className="flex-grow-0 flex-shrink-0 text-sm font-semibold text-left text-[#1f2329]">
                      Thông tin phân ca
                    </p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[500px] gap-4">
                  <div className="flex flex-col justify-start items-start flex-grow gap-1">
                    <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
                      <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5">
                        <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                          Phân công
                        </p>
                        <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                          *
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                      <div className="flex justify-between items-center self-stretch flex-grow relative px-[9px]">
                        <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                          <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                            <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                              Lặp theo tuần
                            </p>
                          </div>
                        </div>
                        <svg
                          width={14}
                          height={15}
                          viewBox="0 0 14 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                          preserveAspectRatio="none"
                        >
                          <path
                            d="M3.5 5.75L6.29289 8.54289C6.62623 8.87623 6.79289 9.04289 7 9.04289C7.20711 9.04289 7.37377 8.87623 7.70711 8.54289L10.5 5.75"
                            stroke="#646A73"
                            stroke-width="1.5"
                            stroke-linecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-4">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[58.25px] gap-1">
                    <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative overflow-hidden gap-[3px]">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                        <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                          Thời gian hiệu lực
                        </p>
                      </div>
                      <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                        *
                      </p>
                    </div>
                    <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                      <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 w-[319px] overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                        <div className="flex justify-between items-center self-stretch flex-grow relative px-[9px]">
                          <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                              <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#8f959e]">
                                Chọn ngày bắt đầu
                              </p>
                            </div>
                          </div>
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <g clipPath="url(#clip0_71_1152)">
                              <path
                                d="M10.5 1.91699V3.08366M3.5 1.91699V3.08366"
                                stroke="#646A73"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M1.4585 7.89189C1.4585 5.35013 1.4585 4.07925 2.1889 3.28962C2.9193 2.5 4.09487 2.5 6.446 2.5H7.55433C9.90546 2.5 11.081 2.5 11.8114 3.28962C12.5418 4.07925 12.5418 5.35013 12.5418 7.89189V8.19144C12.5418 10.7332 12.5418 12.0041 11.8114 12.7937C11.081 13.5833 9.90546 13.5833 7.55433 13.5833H6.446C4.09487 13.5833 2.9193 13.5833 2.1889 12.7937C1.4585 12.0041 1.4585 10.7332 1.4585 8.19144V7.89189Z"
                                stroke="#646A73"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M1.75 5.41699H12.25"
                                stroke="#646A73"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                            </g>
                            <defs>
                              <clipPath id="clip0_71_1152">
                                <rect
                                  width={14}
                                  height={14}
                                  fill="white"
                                  transform="translate(0 0.75)"
                                />
                              </clipPath>
                            </defs>
                          </svg>
                        </div>
                      </div>
                      <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 w-[165px] overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                        <div className="flex justify-between items-center self-stretch flex-grow relative px-[9px]">
                          <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                              <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                Theo khoảng ngày
                              </p>
                            </div>
                          </div>
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.5 6L6.29289 8.79289C6.62623 9.12623 6.79289 9.29289 7 9.29289C7.20711 9.29289 7.37377 9.12623 7.70711 8.79289L10.5 6"
                              stroke="#646A73"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      </div>
                      <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 w-[319px] overflow-hidden py-[9px] rounded-md bg-white border border-[#d0d3d6]">
                        <div className="flex justify-between items-center self-stretch flex-grow relative px-[9px]">
                          <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                              <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#8f959e]">
                                Chọn ngày kết thúc
                              </p>
                            </div>
                          </div>
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <g clipPath="url(#clip0_71_1164)">
                              <path
                                d="M10.5 1.91699V3.08366M3.5 1.91699V3.08366"
                                stroke="#646A73"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M1.4585 7.89189C1.4585 5.35013 1.4585 4.07925 2.1889 3.28962C2.9193 2.5 4.09487 2.5 6.446 2.5H7.55433C9.90546 2.5 11.081 2.5 11.8114 3.28962C12.5418 4.07925 12.5418 5.35013 12.5418 7.89189V8.19144C12.5418 10.7332 12.5418 12.0041 11.8114 12.7937C11.081 13.5833 9.90546 13.5833 7.55433 13.5833H6.446C4.09487 13.5833 2.9193 13.5833 2.1889 12.7937C1.4585 12.0041 1.4585 10.7332 1.4585 8.19144V7.89189Z"
                                stroke="#646A73"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M1.75 5.41699H12.25"
                                stroke="#646A73"
                                stroke-width="1.5"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                            </g>
                            <defs>
                              <clipPath id="clip0_71_1164">
                                <rect
                                  width={14}
                                  height={14}
                                  fill="white"
                                  transform="translate(0 0.75)"
                                />
                              </clipPath>
                            </defs>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-1">
                  <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative overflow-hidden gap-[3px]">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-0.5 pb-[1.25px]">
                      <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                        Ca làm việc áp dụng
                      </p>
                    </div>
                    <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#f54a45]">
                      *
                    </p>
                  </div>
                  <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-4">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-6">
                      <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 gap-2">
                        <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-7 relative py-2 bg-white">
                          <svg
                            width={16}
                            height={16}
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                            preserveAspectRatio="none"
                          >
                            <rect
                              width={16}
                              height={16}
                              rx={2}
                              fill="#008FD3"
                            />
                            <path
                              d="M13.5 8H3"
                              stroke="#FCFCFD"
                              stroke-width="1.3"
                              stroke-linecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                          <div className="flex justify-center items-start flex-grow-0 flex-shrink-0 w-7 relative py-2 bg-white">
                            <svg
                              width={16}
                              height={16}
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                              preserveAspectRatio="none"
                            >
                              <rect
                                width={16}
                                height={16}
                                rx={2}
                                fill="#008FD3"
                              />
                              <path
                                d="M13 4L6 12L3 9"
                                stroke="#FCFCFD"
                                stroke-width="1.3"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-7 relative py-2 bg-white">
                            <svg
                              width={16}
                              height={16}
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                              preserveAspectRatio="none"
                            >
                              <rect
                                width={16}
                                height={16}
                                rx={2}
                                fill="#008FD3"
                              />
                              <path
                                d="M13 4L6 12L3 9"
                                stroke="#FCFCFD"
                                stroke-width="1.3"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-7 relative py-2 bg-white">
                            <svg
                              width={16}
                              height={16}
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                              preserveAspectRatio="none"
                            >
                              <rect
                                width={16}
                                height={16}
                                rx={2}
                                fill="#008FD3"
                              />
                              <path
                                d="M13 4L6 12L3 9"
                                stroke="#FCFCFD"
                                stroke-width="1.3"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-7 relative py-2 bg-white">
                            <svg
                              width={16}
                              height={16}
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                              preserveAspectRatio="none"
                            >
                              <rect
                                width={16}
                                height={16}
                                rx={2}
                                fill="#008FD3"
                              />
                              <path
                                d="M13 4L6 12L3 9"
                                stroke="#FCFCFD"
                                stroke-width="1.3"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-7 relative py-2 bg-white">
                            <svg
                              width={16}
                              height={16}
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                              preserveAspectRatio="none"
                            >
                              <rect
                                width={16}
                                height={16}
                                rx={2}
                                fill="#008FD3"
                              />
                              <path
                                d="M13 4L6 12L3 9"
                                stroke="#FCFCFD"
                                stroke-width="1.3"
                                stroke-linecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-7 py-2 bg-white">
                            <div className="flex flex-col justify-center items-start flex-grow-0 flex-shrink-0">
                              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 relative">
                                <div className="flex-grow-0 flex-shrink-0 w-4 h-4 rounded-sm bg-white border border-[#8f959e]" />
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-7 py-2 bg-white">
                            <div className="flex flex-col justify-center items-start flex-grow-0 flex-shrink-0">
                              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 relative">
                                <div className="flex-grow-0 flex-shrink-0 w-4 h-4 rounded-sm bg-white border border-[#8f959e]" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 gap-2">
                        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden py-[5px]">
                          <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5">
                            <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                              Ngày làm việc
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2.5">
                            <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden pr-6 py-[5px] rounded-md bg-[#8f959e]/10 border border-[#d0d3d6]">
                              <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 px-[9px]">
                                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                    <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                      Thứ 2
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden pr-6 py-[9px] rounded-md bg-[#8f959e]/10 border border-[#d0d3d6]">
                            <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Thứ 3
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden pr-6 py-[9px] rounded-md bg-[#8f959e]/10 border border-[#d0d3d6]">
                            <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Thứ 4
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden pr-6 py-[9px] rounded-md bg-[#8f959e]/10 border border-[#d0d3d6]">
                            <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Thứ 5
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden pr-6 py-[9px] rounded-md bg-[#8f959e]/10 border border-[#d0d3d6]">
                            <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Thứ 6
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden py-[9px] rounded-md bg-[#8f959e]/10 border border-[#d0d3d6]">
                            <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Thứ 7
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden pr-6 py-[9px] rounded-md bg-[#8f959e]/10 border border-[#d0d3d6]">
                            <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Chủ nhật
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-96 gap-2">
                        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden py-[5px]">
                          <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-0.5">
                            <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                              Ca
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
                          <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-[202px] gap-1">
                            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 py-1.5 rounded-md">
                              <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                                  <svg
                                    width={14}
                                    height={14}
                                    viewBox="0 0 14 14"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                    preserveAspectRatio="none"
                                  >
                                    <path
                                      d="M6.99984 1.16699C6.84513 1.16699 6.69675 1.22845 6.58736 1.33785C6.47796 1.44724 6.4165 1.59562 6.4165 1.75033V6.41699H1.74984C1.59513 6.41699 1.44675 6.47845 1.33736 6.58785C1.22796 6.69724 1.1665 6.84562 1.1665 7.00033C1.1665 7.15503 1.22796 7.30341 1.33736 7.4128C1.44675 7.5222 1.59513 7.58366 1.74984 7.58366H6.4165V12.2503C6.4165 12.405 6.47796 12.5534 6.58736 12.6628C6.69675 12.7722 6.84513 12.8337 6.99984 12.8337C7.15455 12.8337 7.30292 12.7722 7.41232 12.6628C7.52171 12.5534 7.58317 12.405 7.58317 12.2503V7.58366H12.2498C12.4045 7.58366 12.5529 7.5222 12.6623 7.4128C12.7717 7.30341 12.8332 7.15503 12.8332 7.00033C12.8332 6.84562 12.7717 6.69724 12.6623 6.58785C12.5529 6.47845 12.4045 6.41699 12.2498 6.41699H7.58317V1.75033C7.58317 1.59562 7.52171 1.44724 7.41232 1.33785C7.30292 1.22845 7.15455 1.16699 6.99984 1.16699Z"
                                      fill="#008FD3"
                                    />
                                  </svg>
                                </div>
                              </div>
                              <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#008fd3]">
                                Thêm ca làm việc
                              </p>
                            </div>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 py-1.5 rounded-md">
                            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                              <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                                <svg
                                  width={14}
                                  height={14}
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                  preserveAspectRatio="none"
                                >
                                  <path
                                    d="M6.99984 1.16699C6.84513 1.16699 6.69675 1.22845 6.58736 1.33785C6.47796 1.44724 6.4165 1.59562 6.4165 1.75033V6.41699H1.74984C1.59513 6.41699 1.44675 6.47845 1.33736 6.58785C1.22796 6.69724 1.1665 6.84562 1.1665 7.00033C1.1665 7.15503 1.22796 7.30341 1.33736 7.4128C1.44675 7.5222 1.59513 7.58366 1.74984 7.58366H6.4165V12.2503C6.4165 12.405 6.47796 12.5534 6.58736 12.6628C6.69675 12.7722 6.84513 12.8337 6.99984 12.8337C7.15455 12.8337 7.30292 12.7722 7.41232 12.6628C7.52171 12.5534 7.58317 12.405 7.58317 12.2503V7.58366H12.2498C12.4045 7.58366 12.5529 7.5222 12.6623 7.4128C12.7717 7.30341 12.8332 7.15503 12.8332 7.00033C12.8332 6.84562 12.7717 6.69724 12.6623 6.58785C12.5529 6.47845 12.4045 6.41699 12.2498 6.41699H7.58317V1.75033C7.58317 1.59562 7.52171 1.44724 7.41232 1.33785C7.30292 1.22845 7.15455 1.16699 6.99984 1.16699Z"
                                    fill="#008FD3"
                                  />
                                </svg>
                              </div>
                            </div>
                            <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#008fd3]">
                              Thêm ca làm việc
                            </p>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 py-1.5 rounded-md">
                            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                              <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                                <svg
                                  width={14}
                                  height={14}
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                  preserveAspectRatio="none"
                                >
                                  <path
                                    d="M6.99984 1.16699C6.84513 1.16699 6.69675 1.22845 6.58736 1.33785C6.47796 1.44724 6.4165 1.59562 6.4165 1.75033V6.41699H1.74984C1.59513 6.41699 1.44675 6.47845 1.33736 6.58785C1.22796 6.69724 1.1665 6.84562 1.1665 7.00033C1.1665 7.15503 1.22796 7.30341 1.33736 7.4128C1.44675 7.5222 1.59513 7.58366 1.74984 7.58366H6.4165V12.2503C6.4165 12.405 6.47796 12.5534 6.58736 12.6628C6.69675 12.7722 6.84513 12.8337 6.99984 12.8337C7.15455 12.8337 7.30292 12.7722 7.41232 12.6628C7.52171 12.5534 7.58317 12.405 7.58317 12.2503V7.58366H12.2498C12.4045 7.58366 12.5529 7.5222 12.6623 7.4128C12.7717 7.30341 12.8332 7.15503 12.8332 7.00033C12.8332 6.84562 12.7717 6.69724 12.6623 6.58785C12.5529 6.47845 12.4045 6.41699 12.2498 6.41699H7.58317V1.75033C7.58317 1.59562 7.52171 1.44724 7.41232 1.33785C7.30292 1.22845 7.15455 1.16699 6.99984 1.16699Z"
                                    fill="#008FD3"
                                  />
                                </svg>
                              </div>
                            </div>
                            <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#008fd3]">
                              Thêm ca làm việc
                            </p>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 py-1.5 rounded-md">
                            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                              <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                                <svg
                                  width={14}
                                  height={14}
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                  preserveAspectRatio="none"
                                >
                                  <path
                                    d="M6.99984 1.16699C6.84513 1.16699 6.69675 1.22845 6.58736 1.33785C6.47796 1.44724 6.4165 1.59562 6.4165 1.75033V6.41699H1.74984C1.59513 6.41699 1.44675 6.47845 1.33736 6.58785C1.22796 6.69724 1.1665 6.84562 1.1665 7.00033C1.1665 7.15503 1.22796 7.30341 1.33736 7.4128C1.44675 7.5222 1.59513 7.58366 1.74984 7.58366H6.4165V12.2503C6.4165 12.405 6.47796 12.5534 6.58736 12.6628C6.69675 12.7722 6.84513 12.8337 6.99984 12.8337C7.15455 12.8337 7.30292 12.7722 7.41232 12.6628C7.52171 12.5534 7.58317 12.405 7.58317 12.2503V7.58366H12.2498C12.4045 7.58366 12.5529 7.5222 12.6623 7.4128C12.7717 7.30341 12.8332 7.15503 12.8332 7.00033C12.8332 6.84562 12.7717 6.69724 12.6623 6.58785C12.5529 6.47845 12.4045 6.41699 12.2498 6.41699H7.58317V1.75033C7.58317 1.59562 7.52171 1.44724 7.41232 1.33785C7.30292 1.22845 7.15455 1.16699 6.99984 1.16699Z"
                                    fill="#008FD3"
                                  />
                                </svg>
                              </div>
                            </div>
                            <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#008fd3]">
                              Thêm ca làm việc
                            </p>
                          </div>
                          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 py-1.5 rounded-md">
                            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                              <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                                <svg
                                  width={14}
                                  height={14}
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                                  preserveAspectRatio="none"
                                >
                                  <path
                                    d="M6.99984 1.16699C6.84513 1.16699 6.69675 1.22845 6.58736 1.33785C6.47796 1.44724 6.4165 1.59562 6.4165 1.75033V6.41699H1.74984C1.59513 6.41699 1.44675 6.47845 1.33736 6.58785C1.22796 6.69724 1.1665 6.84562 1.1665 7.00033C1.1665 7.15503 1.22796 7.30341 1.33736 7.4128C1.44675 7.5222 1.59513 7.58366 1.74984 7.58366H6.4165V12.2503C6.4165 12.405 6.47796 12.5534 6.58736 12.6628C6.69675 12.7722 6.84513 12.8337 6.99984 12.8337C7.15455 12.8337 7.30292 12.7722 7.41232 12.6628C7.52171 12.5534 7.58317 12.405 7.58317 12.2503V7.58366H12.2498C12.4045 7.58366 12.5529 7.5222 12.6623 7.4128C12.7717 7.30341 12.8332 7.15503 12.8332 7.00033C12.8332 6.84562 12.7717 6.69724 12.6623 6.58785C12.5529 6.47845 12.4045 6.41699 12.2498 6.41699H7.58317V1.75033C7.58317 1.59562 7.52171 1.44724 7.41232 1.33785C7.30292 1.22845 7.15455 1.16699 6.99984 1.16699Z"
                                    fill="#008FD3"
                                  />
                                </svg>
                              </div>
                            </div>
                            <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#008fd3]">
                              Thêm ca làm việc
                            </p>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden py-[9px] rounded-md bg-white">
                            <div className="flex justify-start items-center self-stretch flex-grow px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Ngày nghỉ
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden py-[9px] rounded-md bg-white">
                            <div className="flex justify-start items-center self-stretch flex-grow px-[9px]">
                              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0">
                                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative overflow-hidden">
                                  <p className="flex-grow-0 flex-shrink-0 text-[13px] text-left text-[#1f2329]">
                                    Ngày nghỉ
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end items-start self-stretch flex-grow-0 flex-shrink-0">
          <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-3">
            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 relative px-4 py-1 rounded-md bg-white border border-[#d0d3d6]">
              <p className="flex-grow-0 flex-shrink-0 text-[13.671875px] font-medium text-center text-[#1f2329]">
                Hủy bỏ
              </p>
            </div>
            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-8 relative px-4 py-1 rounded-md bg-[#008fd3] border border-[#008fd3]">
              <p className="flex-grow-0 flex-shrink-0 text-[13.78125px] font-semibold text-center text-white">
                Phân ca làm việc
              </p>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
