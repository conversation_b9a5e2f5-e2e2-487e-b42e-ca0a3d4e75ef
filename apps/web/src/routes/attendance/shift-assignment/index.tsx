import ProtectedLayout from '@/components/layout/protected.layout'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Users, Plus, Search, Calendar, Clock } from 'lucide-react'

export const Route = createFileRoute('/attendance/shift-assignment/')({
  component: RouteComponent,
})

interface ShiftAssignment {
  id: string
  employeeName: string
  shiftName: string
  date: string
  startTime: string
  endTime: string
  status: 'assigned' | 'completed' | 'absent'
  unit: string
}

const mockAssignmentData: ShiftAssignment[] = [
  {
    id: '1',
    employeeName: 'Nguyễn Văn A',
    shiftName: 'Ca sáng',
    date: '2024-01-20',
    startTime: '08:00',
    endTime: '16:00',
    status: 'assigned',
    unit: 'Phòng IT'
  },
  {
    id: '2',
    employeeName: 'Trần Thị B',
    shiftName: 'Ca chiều',
    date: '2024-01-20',
    startTime: '14:00',
    endTime: '22:00',
    status: 'completed',
    unit: 'Phòng HR'
  },
  {
    id: '3',
    employeeName: 'Lê Văn C',
    shiftName: 'Ca đêm',
    date: '2024-01-19',
    startTime: '22:00',
    endTime: '06:00',
    status: 'absent',
    unit: 'Bảo vệ'
  }
]

function RouteComponent() {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Badge className="bg-blue-100 text-blue-800">Đã phân ca</Badge>
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Hoàn thành</Badge>
      case 'absent':
        return <Badge className="bg-red-100 text-red-800">Vắng mặt</Badge>
      default:
        return <Badge>Không xác định</Badge>
    }
  }

  return (
    <ProtectedLayout>
      <div className="flex flex-col justify-start items-start w-[1200px] h-[837px] gap-4 px-6 py-5 bg-white">
        <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0 relative">
          <p className="flex-grow-0 flex-shrink-0 text-base font-semibold text-left text-[#1f2329]">
            Danh sách phân ca
          </p>
          <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
            <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
              <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 px-2 py-1.5 rounded-md bg-[#008fd3]">
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                  <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative">
                    <svg
                      width={14}
                      height={14}
                      viewBox="0 0 14 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                      preserveAspectRatio="none"
                    >
                      <path
                        d="M7.00008 1.16699C6.84537 1.16699 6.697 1.22845 6.5876 1.33785C6.47821 1.44724 6.41675 1.59562 6.41675 1.75033V6.41699H1.75008C1.59537 6.41699 1.447 6.47845 1.3376 6.58785C1.22821 6.69724 1.16675 6.84562 1.16675 7.00033C1.16675 7.15503 1.22821 7.30341 1.3376 7.4128C1.447 7.5222 1.59537 7.58366 1.75008 7.58366H6.41675V12.2503C6.41675 12.405 6.47821 12.5534 6.5876 12.6628C6.697 12.7722 6.84537 12.8337 7.00008 12.8337C7.15479 12.8337 7.30316 12.7722 7.41256 12.6628C7.52196 12.5534 7.58341 12.405 7.58341 12.2503V7.58366H12.2501C12.4048 7.58366 12.5532 7.5222 12.6626 7.4128C12.772 7.30341 12.8334 7.15503 12.8334 7.00033C12.8334 6.84562 12.772 6.69724 12.6626 6.58785C12.5532 6.47845 12.4048 6.41699 12.2501 6.41699H7.58341V1.75033C7.58341 1.59562 7.52196 1.44724 7.41256 1.33785C7.30316 1.22845 7.15479 1.16699 7.00008 1.16699Z"
                        fill="white"
                      />
                    </svg>
                  </div>
                </div>
                <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-white">
                  Thêm mới phân ca
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-2">
          <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 w-[250px]">
            <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-1.5">
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden rounded-md border border-[#d0d3d6]">
                <div className="flex justify-end items-center flex-grow relative gap-2 px-3 py-2 bg-white border-0 border-[#dde4ee]">
                  <svg
                    width={16}
                    height={16}
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="flex-grow-0 flex-shrink-0 w-4 h-4 relative"
                    preserveAspectRatio="none"
                  >
                    <g clip-path="url(#clip0_71_1009)">
                      <path
                        d="M11.6667 11.667L14.6667 14.667"
                        stroke="#53637A"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M13.3333 7.33301C13.3333 4.0193 10.647 1.33301 7.33325 1.33301C4.01954 1.33301 1.33325 4.0193 1.33325 7.33301C1.33325 10.6467 4.01954 13.333 7.33325 13.333C10.647 13.333 13.3333 10.6467 13.3333 7.33301Z"
                        stroke="#53637A"
                        strokeLinejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_71_1009">
                        <rect width={16} height={16} fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  <div className="flex justify-start items-center flex-grow relative">
                    <p className="flex-grow-0 flex-shrink-0 text-xs text-left text-[#8f959e]">
                      Theo mã hoặc tên bảng phân ca
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative gap-2 px-3 py-1.5 rounded-md bg-white border border-[#d0d3d6]">
              <svg
                width={14}
                height={14}
                viewBox="0 0 14 14"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                preserveAspectRatio="none"
              >
                <path
                  d="M5.16685 7.29522C3.71525 6.20994 2.68079 5.01617 2.11595 4.34505C1.9411 4.13731 1.88381 3.98527 1.84936 3.71747C1.7314 2.80047 1.67242 2.34197 1.94131 2.04598C2.21019 1.75 2.68569 1.75 3.63669 1.75H10.3633C11.3143 1.75 11.7898 1.75 12.0587 2.04598C12.3276 2.34197 12.2686 2.80047 12.1506 3.71747C12.1162 3.98528 12.0589 4.13731 11.884 4.34506C11.3184 5.01703 10.2819 6.21291 8.82733 7.29957C8.69574 7.39788 8.609 7.5581 8.59291 7.73582C8.44882 9.32865 8.31594 10.2011 8.23323 10.6424C8.09973 11.355 7.08935 11.7837 6.54851 12.1662C6.22653 12.3939 5.83584 12.1228 5.79412 11.7704C5.71458 11.0986 5.56477 9.73372 5.40124 7.73582C5.38655 7.55646 5.2995 7.39438 5.16685 7.29522Z"
                  stroke="#141B34"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-center text-[#1f2329]">
                Bộ lọc
              </p>
            </div>
          </div>
        </div>
        <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-4">
          <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-1">
            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
              <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#646a73]">
                Tổng:
              </p>
            </div>
            <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
              <p className="flex-grow-0 flex-shrink-0 text-[13px] font-semibold text-left text-[#1f2329]">
                10 bản ghi
              </p>
            </div>
          </div>
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0">
            <div className="flex flex-col-reverse justify-start items-start self-stretch flex-grow-0 flex-shrink-0 overflow-hidden">
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-center items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2 bg-white">

                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2 bg-white">
                  <p className="flex-grow w-[187px] text-sm text-left text-[#1f2329]">
                    01/07/2024 - Vô thời hạn
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] px-3 py-2 bg-white">
                  <div className="flex justify-start items-center flex-grow relative gap-1.5">
                    <div className="flex justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-2 h-2 gap-1 px-1.5 rounded-lg bg-[#35c724]" />
                    </div>
                    <p className="flex-grow w-28 text-sm text-left text-[#1f2329]">Đang áp dụng</p>
                  </div>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 space-x-[-7.105427357601002e-15px] px-3 py-2 bg-white">
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative rounded-md">
                      <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#008fd3]">
                        Chi tiết
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 h-[22px] pl-2">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 h-[22px]">
                      <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 px-1 py-0.5 rounded-md">
                        <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pt-[3.950000047683716px] pb-[4.050000190734863px]">
                          <svg
                            width={14}
                            height={15}
                            viewBox="0 0 14 15"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                            preserveAspectRatio="none"
                          >
                            <path
                              d="M3.20841 7.80404C3.20841 8.07478 3.10086 8.33443 2.90942 8.52587C2.71798 8.71732 2.45832 8.82487 2.18758 8.82487C1.91684 8.82487 1.65719 8.71732 1.46574 8.52587C1.2743 8.33443 1.16675 8.07478 1.16675 7.80404C1.16675 7.53329 1.2743 7.27364 1.46574 7.0822C1.65719 6.89075 1.91684 6.7832 2.18758 6.7832C2.45832 6.7832 2.71798 6.89075 2.90942 7.0822C3.10086 7.27364 3.20841 7.53329 3.20841 7.80404ZM8.00633 7.80404C8.00633 8.07478 7.89878 8.33443 7.70734 8.52587C7.51589 8.71732 7.25624 8.82487 6.9855 8.82487C6.71476 8.82487 6.4551 8.71732 6.26366 8.52587C6.07222 8.33443 5.96466 8.07478 5.96466 7.80404C5.96466 7.53329 6.07222 7.27364 6.26366 7.0822C6.4551 6.89075 6.71476 6.7832 6.9855 6.7832C7.25624 6.7832 7.51589 6.89075 7.70734 7.0822C7.89878 7.27364 8.00633 7.53329 8.00633 7.80404ZM12.8334 7.80404C12.8334 8.07478 12.7259 8.33443 12.5344 8.52587C12.343 8.71732 12.0833 8.82487 11.8126 8.82487C11.5418 8.82487 11.2822 8.71732 11.0907 8.52587C10.8993 8.33443 10.7917 8.07478 10.7917 7.80404C10.7917 7.53329 10.8993 7.27364 11.0907 7.0822C11.2822 6.89075 11.5418 6.7832 11.8126 6.7832C12.0833 6.7832 12.343 6.89075 12.5344 7.0822C12.7259 7.27364 12.8334 7.53329 12.8334 7.80404Z"
                              fill="#008FD3"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2 p-2 bg-[#f2f3f5]/70 border-t-0 border-r-0 border-b border-l-0 border-[#dee0e3]">
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[140px] relative px-3 py-2">
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[345px] relative px-3 py-2">
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2">
                  <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#3f4f66]">
                    Loại ca
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow relative px-3 py-2">
                  <p className="flex-grow w-[187px] text-sm font-medium text-left text-[#3f4f66]">
                    Thời gian hiệu lực
                  </p>
                </div>
                <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 w-[150px] relative px-3 py-2">
                  <p className="flex-grow w-[126px] text-sm font-medium text-left text-[#3f4f66]">
                    Trạng thái
                  </p>
                </div>
                <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 w-[100px] h-10 relative space-x-[-7.105427357601002e-15px] px-3 py-2">
                  <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-left text-[#3f4f66]">
                    Thao tác
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center self-stretch flex-grow-0 flex-shrink-0">
            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-2">
              <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#646a73]">
                  Hiển thị:
                </p>
              </div>
              <div className="flex justify-start items-start flex-grow-0 flex-shrink-0 gap-1">
                <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 h-7 rounded border border-[#d0d3d6]">
                  <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 gap-2 px-3">
                    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                      <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative">
                        <p className="flex-grow-0 flex-shrink-0 text-[13px] font-medium text-left text-[#1f2329]">
                          10 bản ghi
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-4 h-4 relative overflow-hidden gap-2.5">
                      <svg
                        width={14}
                        height={14}
                        viewBox="0 0 14 14"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="flex-grow-0 flex-shrink-0 w-3.5 h-3.5 relative"
                        preserveAspectRatio="none"
                      >
                        <g clip-path="url(#clip0_71_982)">
                          <path
                            d="M1.99153 4.13374L1.57906 4.54623C1.35125 4.77403 1.35125 5.14338 1.57906 5.37118L6.11633 9.90845C6.57193 10.3641 7.31062 10.3641 7.76624 9.90845L12.3035 5.37118C12.5313 5.14338 12.5313 4.77403 12.3035 4.54623L11.8911 4.13374C11.6632 3.90594 11.2939 3.90594 11.0661 4.13374L6.94128 8.25854L2.81649 4.13374C2.58869 3.90594 2.21933 3.90594 1.99153 4.13374Z"
                            fill="#1F2329"
                          />
                        </g>
                        <defs>
                          <clipPath id="clip0_71_982">
                            <rect width={14} height={14} fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-start items-start self-stretch flex-grow-0 flex-shrink-0 gap-2">
              <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-2">
                <div className="flex justify-center items-start flex-grow-0 flex-shrink-0 rounded-sm">
                  <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 px-[7px] pt-[6.5px] pb-[7.5px] rounded-sm">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative">
                      <svg
                        width={13}
                        height={13}
                        viewBox="0 0 13 13"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="flex-grow-0 flex-shrink-0 w-3 h-3 relative"
                        preserveAspectRatio="none"
                      >
                        <path
                          d="M9.15944 2.56646V1.53119C9.15944 1.44146 9.05632 1.3919 8.98668 1.44681L2.94918 6.16244C2.89788 6.20233 2.85637 6.25341 2.82782 6.31179C2.79927 6.37016 2.78442 6.43429 2.78442 6.49927C2.78442 6.56425 2.79927 6.62838 2.82782 6.68675C2.85637 6.74512 2.89788 6.79621 2.94918 6.8361L8.98668 11.5517C9.05766 11.6066 9.15944 11.5571 9.15944 11.4673V10.4321C9.15944 10.3665 9.12864 10.3035 9.07775 10.2633L4.25632 6.49994L9.07775 2.73521C9.12864 2.69503 9.15944 2.63208 9.15944 2.56646Z"
                          fill="#1F2329"
                          fillOpacity="0.5"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="flex justify-center items-start flex-grow-0 flex-shrink-0 px-px pt-px pb-0.5 rounded-sm border border-[#008fd3]">
                  <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pl-[9.90999984741211px] pr-[9.930000305175781px]">
                    <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#1f2329]">
                      1
                    </p>
                  </div>
                </div>
                <div className="flex justify-center items-start flex-grow-0 flex-shrink-0 px-px pt-px pb-0.5 rounded-sm">
                  <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 relative pl-[9.90999984741211px] pr-[9.930000305175781px]">
                    <p className="flex-grow-0 flex-shrink-0 text-sm font-medium text-center text-[#1f2329]">
                      2
                    </p>
                  </div>
                </div>
                <div className="flex justify-center items-start flex-grow-0 flex-shrink-0 rounded-sm">
                  <div className="flex flex-col justify-start items-center flex-grow-0 flex-shrink-0 px-[7px] pt-[6.5px] pb-[7.5px] rounded-sm">
                    <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative">
                      <svg
                        width={12}
                        height={13}
                        viewBox="0 0 12 13"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="flex-grow-0 flex-shrink-0 w-3 h-3 relative"
                        preserveAspectRatio="none"
                      >
                        <path
                          d="M9.39777 6.16233L3.36027 1.4467C3.34449 1.43428 3.32553 1.42656 3.30557 1.42443C3.2856 1.4223 3.26544 1.42584 3.2474 1.43466C3.22936 1.44347 3.21417 1.45719 3.20357 1.47424C3.19298 1.4913 3.18741 1.511 3.1875 1.53108V2.56634C3.1875 2.63197 3.2183 2.69491 3.2692 2.73509L8.09063 6.49983L3.2692 10.2646C3.21697 10.3047 3.1875 10.3677 3.1875 10.4333V11.4686C3.1875 11.5583 3.29063 11.6079 3.36027 11.553L9.39777 6.83733C9.44908 6.7973 9.4906 6.7461 9.51915 6.68761C9.5477 6.62913 9.56254 6.56491 9.56254 6.49983C9.56254 6.43475 9.5477 6.37052 9.51915 6.31204C9.4906 6.25356 9.44908 6.20235 9.39777 6.16233Z"
                          fill="#1F2329"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
