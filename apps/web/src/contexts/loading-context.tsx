import { createContext, ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react'

interface LoadingItem {
  id: string
  message: string
  priority: number
  startTime: number
}

interface LoadingContextType {
  isLoading: boolean
  isVisible: boolean
  loadingCount: number
  startLoading: (id?: string, message?: string, priority?: number) => string
  stopLoading: (id?: string) => void
  setLoadingMessage: (message: string) => void
  loadingMessage: string
  clearAllLoading: () => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

interface LoadingProviderProps {
  children: ReactNode
  showDelay?: number // Delay before showing loading (default: 200ms)
  minDisplayTime?: number // Minimum time to show loading (default: 500ms)
}

export function LoadingProvider({
  children,
  showDelay = 200,
  minDisplayTime = 500
}: LoadingProviderProps) {
  const [loadingItems, setLoadingItems] = useState<Map<string, LoadingItem>>(new Map())
  const [isVisible, setIsVisible] = useState(false)
  const [currentMessage, setCurrentMessage] = useState('Đang xử lý...')

  // Refs for timers
  const showTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const loadingStartTimeRef = useRef<number | null>(null)

  // Computed values
  const isLoading = loadingItems.size > 0
  const loadingCount = loadingItems.size

  // Get highest priority message
  const getHighestPriorityMessage = useCallback(() => {
    if (loadingItems.size === 0) return 'Đang xử lý...'

    let highestPriority = -1
    let message = 'Đang xử lý...'

    loadingItems.forEach((item) => {
      if (item.priority > highestPriority) {
        highestPriority = item.priority
        message = item.message
      }
    })

    return message
  }, [loadingItems])

  // Update current message when loading items change
  const updateMessage = useCallback(() => {
    const newMessage = getHighestPriorityMessage()
    setCurrentMessage(newMessage)
  }, [getHighestPriorityMessage])

  // Start loading function
  const startLoading = useCallback((id?: string, message = 'Đang xử lý...', priority = 0) => {
    const loadingId = id || `loading-${Date.now()}-${Math.random()}`

    setLoadingItems(prev => {
      const newMap = new Map(prev)
      newMap.set(loadingId, {
        id: loadingId,
        message,
        priority,
        startTime: Date.now()
      })
      return newMap
    })

    // Clear any pending hide timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }

    // If this is the first loading item and not visible yet
    if (loadingItems.size === 0 && !isVisible) {
      // Clear any pending show timeout
      if (showTimeoutRef.current) {
        clearTimeout(showTimeoutRef.current)
      }

      // Set timeout to show loading after delay
      showTimeoutRef.current = setTimeout(() => {
        setIsVisible(true)
        loadingStartTimeRef.current = Date.now()
        showTimeoutRef.current = null
      }, showDelay)
    }

    return loadingId
  }, [loadingItems.size, isVisible, showDelay])

  // Stop loading function
  const stopLoading = useCallback((id?: string) => {
    if (!id) return

    setLoadingItems(prev => {
      const newMap = new Map(prev)
      if (newMap.has(id)) {
        newMap.delete(id)
      }
      return newMap
    })
  }, [])

  // Handle hiding loading with minimum display time
  const handleHideLoading = useCallback(() => {
    if (!loadingStartTimeRef.current) {
      setIsVisible(false)
      return
    }

    const elapsedTime = Date.now() - loadingStartTimeRef.current
    const remainingTime = Math.max(0, minDisplayTime - elapsedTime)

    if (remainingTime > 0) {
      // Wait for minimum display time
      hideTimeoutRef.current = setTimeout(() => {
        setIsVisible(false)
        loadingStartTimeRef.current = null
        hideTimeoutRef.current = null
      }, remainingTime)
    } else {
      // Hide immediately
      setIsVisible(false)
      loadingStartTimeRef.current = null
    }
  }, [minDisplayTime])

  // Effect to handle visibility changes
  useEffect(() => {
    if (loadingItems.size === 0 && isVisible) {
      // No more loading items, hide loading
      handleHideLoading()
    } else if (loadingItems.size > 0) {
      // Update message for current loading items
      updateMessage()
    }
  }, [loadingItems.size, isVisible, handleHideLoading, updateMessage])

  // Clear all loading function
  const clearAllLoading = useCallback(() => {
    setLoadingItems(new Map())

    // Clear all timeouts
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current)
      showTimeoutRef.current = null
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }

    setIsVisible(false)
    loadingStartTimeRef.current = null
  }, [])

  // Set loading message function (for backward compatibility)
  const setLoadingMessage = useCallback((message: string) => {
    setCurrentMessage(message)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (showTimeoutRef.current) {
        clearTimeout(showTimeoutRef.current)
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [])

  const value: LoadingContextType = {
    isLoading,
    isVisible,
    loadingCount,
    startLoading,
    stopLoading,
    setLoadingMessage,
    loadingMessage: currentMessage,
    clearAllLoading,
  }

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  )
}

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

// Hook for automatic loading management
export function useApiLoading() {
  const { startLoading, stopLoading, setLoadingMessage } = useLoading()

  const withLoading = useCallback(
    async <T,>(
      apiCall: () => Promise<T>,
      options?: {
        message?: string
        priority?: number
        id?: string
      }
    ): Promise<T> => {
      const { message = 'Đang xử lý...', priority = 0, id } = options || {}
      const loadingId = startLoading(id, message, priority)

      try {
        const result = await apiCall()
        return result
      } finally {
        stopLoading(loadingId)
      }
    },
    [startLoading, stopLoading]
  )

  return { withLoading, setLoadingMessage }
}

// Hook for manual loading control with better UX
export function useLoadingControl() {
  const { startLoading, stopLoading, clearAllLoading, isLoading, isVisible, loadingCount } = useLoading()

  const showLoading = useCallback((message = 'Đang xử lý...', priority = 0) => {
    return startLoading(undefined, message, priority)
  }, [startLoading])

  const hideLoading = useCallback((id: string) => {
    stopLoading(id)
  }, [stopLoading])

  const showCriticalLoading = useCallback((message = 'Đang xử lý...') => {
    return startLoading(undefined, message, 10) // High priority
  }, [startLoading])

  return {
    showLoading,
    hideLoading,
    showCriticalLoading,
    clearAllLoading,
    isLoading,
    isVisible,
    loadingCount
  }
}
