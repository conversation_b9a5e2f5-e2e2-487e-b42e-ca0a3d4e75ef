import { useApiQuery } from '@/shared/hooks/use-api-query'
// Temporarily comment out @c-cam/types import due to module resolution issues
// import {
//   UserResponse,
//   UserQueryParams,
//   UserListResponse,
//   SingleUserResponse,
//   UserStatus
// } from '@c-cam/types'

// Temporary local type definitions
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

export enum UserGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export interface UserResponse {
  _id?: string // MongoDB ObjectId
  id: string
  unit_id: string
  face_id?: string
  avatar_id?: string
  member_role_id?: string
  code: string
  name: string
  email?: string
  phone?: string
  dob?: Date
  gender?: UserGender
  username: string
  status: UserStatus
  created_by: string
  created_at: Date
  updated_at?: Date
  full_name?: string
  first_name?: string
  last_name?: string
  // Populated fields from backend
  tenant?: {
    id: string
    name: string
  }
  unit?: {
    id: string
    name: string
  }
}

export interface UserQueryParams {
  limit?: number
  skip?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
  status?: UserStatus
  unit_id?: string
  search?: string
}

export interface UserListResponse {
  users: UserResponse[]
  total?: number
  page?: number
  limit?: number
}

export interface SingleUserResponse {
  user: UserResponse
}

// Re-export types for convenience
export type Member = UserResponse
export type MemberQueryParams = UserQueryParams
export type MemberListResponse = UserListResponse
export type MemberResponse = SingleUserResponse
// UserStatus is already exported above as enum;

/**
 * Hook to fetch all members/users with optional pagination and sorting
 */
export const useMembersQuery = (params?: MemberQueryParams) => {
  return useApiQuery<MemberListResponse>(
    ['members', ...(params ? [JSON.stringify(params)] : [])],
    '/api/users',
    params,
  )
}

/**
 * Hook to fetch a single member by ID
 */
export const useMemberQuery = (id: string, enabled = true) => {
  return useApiQuery<MemberResponse>(
    ['members', id],
    `/api/users/${id}`,
    undefined,
    { enabled: enabled && !!id },
  )
}

/**
 * Hook to fetch members by unit ID with pagination
 */
export const useMembersByUnitQuery = (
  unitId: string,
  enabled = true,
  pageSize = 10,
  pageIndex = 0,
) => {
  return useApiQuery<
    MemberListResponse & {
      pageSize: number
      pageIndex: number
      totalPages: number
      totalCount: number
    }
  >(
    ['members', 'unit', unitId, pageSize.toString(), pageIndex.toString()],
    `/api/users/unit/${unitId}?pageSize=${pageSize}&pageIndex=${pageIndex}`,
    undefined,
    {
      enabled: enabled && !!unitId,
      placeholderData: {
        users: [],
        pageSize: 10,
        pageIndex: 0,
        totalPages: 0,
        totalCount: 0,
      },
    },
  )
}

/**
 * Hook to fetch member by username
 */
export const useMemberByUsernameQuery = (username: string, enabled = true) => {
  return useApiQuery<MemberResponse>(
    ['members', 'username', username],
    `/api/users/username/${username}`,
    undefined,
    { enabled: enabled && !!username },
  )
}

/**
 * Hook to fetch member by email
 */
export const useMemberByEmailQuery = (email: string, enabled = true) => {
  return useApiQuery<MemberResponse>(
    ['members', 'email', email],
    `/api/users/email/${email}`,
    undefined,
    { enabled: enabled && !!email },
  )
}

/**
 * Hook to fetch members created by a specific user
 */
export const useMembersByCreatorQuery = (createdBy: string, enabled = true) => {
  return useApiQuery<MemberListResponse>(
    ['members', 'created-by', createdBy],
    `/api/users/created-by/${createdBy}`,
    undefined,
    { enabled: enabled && !!createdBy },
  )
}

/**
 * Hook to fetch all members by tenant ID with pagination
 * Gets tenant ID from access token instead of requiring it as parameter
 */
export const useMembersByTenantQuery = (
  enabled = true,
  pageSize = 10,
  pageIndex = 0,
) => {
  console.log('useMembersByTenantQuery called with:', {
    enabled,
    pageSize,
    pageIndex,
  })

  return useApiQuery<
    MemberListResponse & {
      pageSize: number
      pageIndex: number
      totalPages: number
      totalCount: number
    }
  >(
    ['members', pageSize.toString(), pageIndex.toString()],
    `/api/users?pageSize=${pageSize}&pageIndex=${pageIndex}`,
    undefined,
    {
      enabled: enabled,
      // Provide default data to prevent undefined
      placeholderData: {
        users: [],
        pageSize: 10,
        pageIndex: 0,
        totalPages: 0,
        totalCount: 0,
      },
      // Retry on failure
      retry: 3,
      retryDelay: 1000,
    },
  )
}
