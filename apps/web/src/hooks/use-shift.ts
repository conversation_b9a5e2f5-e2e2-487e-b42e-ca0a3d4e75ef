import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { UseMutationOptions, UseQueryOptions } from '@tanstack/react-query'
import { useApiQuery, useCreateMutation, useUpdateMutation, useDeleteMutation } from '@/shared/hooks/use-api-query'
import { APIResponse } from '@c-cam/types'

export interface ShiftFormData {
  name: string;
  type: 'Fixed' | 'Flexible';
  work_coefficient: number;
  tenant_id: string;
}

export interface ShiftDetailFormData {
  tenant_id: string;
  shift_start?: Date; // Thời gian bắt đầu ca làm việc
  clock_in_start?: Date; // Thời gian bắt đầu cho phép chấm công
  allow_late: boolean; // Cho phép đến muộn
  late_cutoff: boolean; // đến sau quá số phút sẽ không ghi nhận nửa ca đầu
  late_allowance?: number; // <PERSON><PERSON> phút cho phép đến muộn
  late_threshold?: number; // Số phút vào sau sẽ không ghi nhận nửa ca đầu
  has_break: boolean; // Nghỉ giữa giờ
  break_start?: Date; // Thời gian bắt đầu nghỉ giữa giờ
  break_end?: Date; // Thời gian kết thúc nghỉ giữa giờ
  shift_end?: Date; // Thời gian kết thúc ca
  clock_out_latest?: Date; // Thời gian muộn nhất cho phép chấm công ra
  allow_early_out: boolean; // Cho phép chấm công ra trước
  early_threshold?: number; // Số phút chấm công ra trước quy định bị tính là về sớm
  early_no_half_shift: boolean; // Cho phép không ghi nhận nửa công ca sau nếu về sớm
  early_cutoff?: number; // Số phút cho phép về sớm nếu vượt quá sẽ không được tính công nửa ca sau
  worked_hours?: number; // Số giờ làm việc
  clock_in_at_start: boolean; // yêu cầu chấm công
  late_threshold_all_shift: number; // Số phút cho phép đến muộn (quá không ghi nhận công cho toàn bộ ca làm việc)
}

export interface CreateShiftRequest {
  shiftData: ShiftFormData;
  shiftDetailData: ShiftDetailFormData;
}

export interface ShiftResponse {
  _id: string;
  tenant_id: string;
  shift_detail_id?: string;
  code: number;
  name: string;
  type: 'Fixed' | 'Flexible';
  work_coefficient: number;
  created_by: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface ShiftDetailResponse {
  _id: string;
  tenant_id: string;
  shift_start?: Date;
  clock_in_start?: Date;
  allow_late: boolean;
  late_cutoff: boolean;
  late_allowance?: number;
  late_threshold?: number;
  has_break: boolean;
  break_start?: Date;
  break_end?: Date;
  shift_end?: Date;
  clock_out_latest?: Date;
  allow_early_out: boolean;
  early_threshold?: number;
  early_no_half_shift: boolean;
  early_cutoff?: number;
  worked_hours?: number;
  clock_in_at_start: boolean;
  late_threshold_all_shift: number;
}

export interface CreateShiftWithDetailResponse {
  shift: ShiftResponse;
  shiftDetail: ShiftDetailResponse;
}

/**
 * Hook to get all shifts (/api/shifts)
 */
export const useShiftsQuery = (
  params?: Record<string, any>,
  options?: Omit<
    UseQueryOptions<{ shifts: ShiftResponse[] }, Error>,
    'queryKey' | 'queryFn'
  >,
) => {
  const queryKey = params ? ['shifts', JSON.stringify(params)] : ['shifts']
  return useApiQuery<{ shifts: ShiftResponse[] }>(
    queryKey,
    '/api/shifts',
    params,
    options,
  )
}

/**
 * Hook to get a specific shift by ID (/api/shifts/:id)
 */
export const useShiftQuery = (
  id: string,
  options?: Omit<
    UseQueryOptions<{ shift: ShiftResponse }, Error>,
    'queryKey' | 'queryFn'
  >,
) => {
  return useApiQuery<{ shift: ShiftResponse }>(
    ['shifts', id],
    `/api/shifts/${id}`,
    undefined,
    {
      enabled: !!id,
      ...options,
    },
  )
}

/**
 * Hook to get all shift details (/api/shift-details)
 */
export const useShiftDetailsQuery = (
  params?: Record<string, any>,
  options?: Omit<
    UseQueryOptions<{ shiftDetails: ShiftDetailResponse[] }, Error>,
    'queryKey' | 'queryFn'
  >,
) => {
  const queryKey = params ? ['shift-details', JSON.stringify(params)] : ['shift-details']
  return useApiQuery<{ shiftDetails: ShiftDetailResponse[] }>(
    queryKey,
    '/api/shift-details',
    params,
    options,
  )
}

/**
 * Hook to get a specific shift detail by ID (/api/shift-details/:id)
 */
export const useShiftDetailQuery = (
  id: string,
  options?: Omit<
    UseQueryOptions<{ shiftDetail: ShiftDetailResponse }, Error>,
    'queryKey' | 'queryFn'
  >,
) => {
  return useApiQuery<{ shiftDetail: ShiftDetailResponse }>(
    ['shift-details', id],
    `/api/shift-details/${id}`,
    undefined,
    {
      enabled: !!id,
      ...options,
    },
  )
}

/**
 * Hook for creating a shift with shift detail (/api/shifts/with-detail)
 */
export const useCreateShiftWithDetailMutation = (
  options?: UseMutationOptions<
    APIResponse<CreateShiftWithDetailResponse>,
    Error,
    CreateShiftRequest
  >,
) => {
  const queryClient = useQueryClient()

  return useCreateMutation<
    APIResponse<CreateShiftWithDetailResponse>,
    CreateShiftRequest,
    Error
  >('/api/shifts/with-detail', {
    ...options,
    meta: {
      successMessage: 'Tạo ca làm việc thành công',
      errorMessage: 'Có lỗi xảy ra khi tạo ca làm việc',
      ...options?.meta,
    },
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch shifts and shift details
      queryClient.invalidateQueries({ queryKey: ['shifts'] })
      queryClient.invalidateQueries({ queryKey: ['shift-details'] })

      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
  })
}

/**
 * Hook for creating a shift only (/api/shifts)
 */
export const useCreateShiftMutation = (
  options?: UseMutationOptions<
    APIResponse<{ shift: ShiftResponse }>,
    Error,
    ShiftFormData
  >,
) => {
  const queryClient = useQueryClient()

  return useCreateMutation<
    APIResponse<{ shift: ShiftResponse }>,
    ShiftFormData,
    Error
  >('/api/shifts', {
    ...options,
    meta: {
      successMessage: 'Tạo shift thành công',
      errorMessage: 'Có lỗi xảy ra khi tạo shift',
      ...options?.meta,
    },
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch shifts
      queryClient.invalidateQueries({ queryKey: ['shifts'] })

      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
  })
}

/**
 * Hook for creating a shift detail only (/api/shift-details)
 */
export const useCreateShiftDetailMutation = (
  options?: UseMutationOptions<
    APIResponse<{ shiftDetail: ShiftDetailResponse }>,
    Error,
    ShiftDetailFormData
  >,
) => {
  const queryClient = useQueryClient()

  return useCreateMutation<
    APIResponse<{ shiftDetail: ShiftDetailResponse }>,
    ShiftDetailFormData,
    Error
  >('/api/shift-details', {
    ...options,
    meta: {
      successMessage: 'Tạo shift detail thành công',
      errorMessage: 'Có lỗi xảy ra khi tạo shift detail',
      ...options?.meta,
    },
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch shift details
      queryClient.invalidateQueries({ queryKey: ['shift-details'] })

      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
  })
}

/**
 * Hook for updating a shift (/api/shifts/:id)
 */
export const useUpdateShiftMutation = (
  id: string,
  options?: UseMutationOptions<
    APIResponse<{ success: boolean }>,
    Error,
    Partial<ShiftFormData>
  >,
) => {
  const queryClient = useQueryClient()

  return useUpdateMutation<
    APIResponse<{ success: boolean }>,
    Partial<ShiftFormData>,
    Error
  >(`/api/shifts/${id}`, {
    ...options,
    meta: {
      successMessage: 'Cập nhật shift thành công',
      errorMessage: 'Có lỗi xảy ra khi cập nhật shift',
      ...options?.meta,
    },
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch shifts
      queryClient.invalidateQueries({ queryKey: ['shifts'] })
      queryClient.invalidateQueries({ queryKey: ['shifts', id] })

      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
  })
}

/**
 * Hook for updating a shift detail (/api/shift-details/:id)
 */
export const useUpdateShiftDetailMutation = (
  id: string,
  options?: UseMutationOptions<
    APIResponse<{ success: boolean }>,
    Error,
    Partial<ShiftDetailFormData>
  >,
) => {
  const queryClient = useQueryClient()

  return useUpdateMutation<
    APIResponse<{ success: boolean }>,
    Partial<ShiftDetailFormData>,
    Error
  >(`/api/shift-details/${id}`, {
    ...options,
    meta: {
      successMessage: 'Cập nhật shift detail thành công',
      errorMessage: 'Có lỗi xảy ra khi cập nhật shift detail',
      ...options?.meta,
    },
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch shift details
      queryClient.invalidateQueries({ queryKey: ['shift-details'] })
      queryClient.invalidateQueries({ queryKey: ['shift-details', id] })

      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
  })
}

/**
 * Hook for deleting a shift (/api/shifts/:id)
 */
export const useDeleteShiftMutation = (
  id: string,
  options?: UseMutationOptions<
    APIResponse<{ success: boolean }>,
    Error,
    void
  >,
) => {
  const queryClient = useQueryClient()

  return useDeleteMutation<
    APIResponse<{ success: boolean }>,
    void,
    Error
  >(`/api/shifts/${id}`, {
    ...options,
    meta: {
      successMessage: 'Xóa shift thành công',
      errorMessage: 'Có lỗi xảy ra khi xóa shift',
      ...options?.meta,
    },
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch shifts
      queryClient.invalidateQueries({ queryKey: ['shifts'] })
      queryClient.removeQueries({ queryKey: ['shifts', id] })

      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
  })
}

/**
 * Hook for deleting a shift detail (/api/shift-details/:id)
 */
export const useDeleteShiftDetailMutation = (
  id: string,
  options?: UseMutationOptions<
    APIResponse<{ success: boolean }>,
    Error,
    void
  >,
) => {
  const queryClient = useQueryClient()

  return useDeleteMutation<
    APIResponse<{ success: boolean }>,
    void,
    Error
  >(`/api/shift-details/${id}`, {
    ...options,
    meta: {
      successMessage: 'Xóa shift detail thành công',
      errorMessage: 'Có lỗi xảy ra khi xóa shift detail',
      ...options?.meta,
    },
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch shift details
      queryClient.invalidateQueries({ queryKey: ['shift-details'] })
      queryClient.removeQueries({ queryKey: ['shift-details', id] })

      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
  })
}

// Legacy hook for backward compatibility
export const useShift = () => {
  const createShiftMutation = useCreateShiftWithDetailMutation()
  const shiftsQuery = useShiftsQuery()

  return {
    loading: createShiftMutation.isPending || shiftsQuery.isLoading,
    createShift: createShiftMutation.mutateAsync,
    getShifts: () => shiftsQuery.data?.shifts || [],
    // Expose the mutation and query objects for more control
    createShiftMutation,
    shiftsQuery,
  }
}
