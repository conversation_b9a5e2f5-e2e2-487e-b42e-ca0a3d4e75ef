import { useGlobalLoading } from '@/hooks/use-global-loading'
import { useIdentityActions } from '@/hooks/use-identity-actions'
import { useApiMutation } from '@/shared/hooks/use-api-query'
import { useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo } from 'react'
import { toast } from 'sonner'
import { useSelectedTenant } from './use-selected-tenant'
import type { Tenant, TenantQueryParams } from './use-tenant-query'
import { useTenantsQuery } from './use-tenant-query'

/**
 * Interface for creating a new tenant
 */
export interface CreateTenantRequest {
  name: string
}

/**
 * Interface for updating a tenant
 */
export interface UpdateTenantRequest {
  name: string
}

/**
 * Hook for handling tenant actions and UI interactions
 */
export const useTenantActions = (queryParams?: TenantQueryParams) => {
  const queryClient = useQueryClient()

  // Fetch tenants data
  const tenantsQuery = useTenantsQuery(queryParams)

  // Debug logging
  useEffect(() => {
    if (tenantsQuery.error) {
      console.error('Tenants query error:', tenantsQuery.error)
    }
    if (tenantsQuery.data) {
      console.log('Tenants query data:', tenantsQuery.data)
    }
  }, [tenantsQuery.error, tenantsQuery.data])

  // Create tenant mutation
  const createTenantMutation = useApiMutation<
    { organization: Tenant },
    CreateTenantRequest
  >('/api/tenants', 'POST', {
    onSuccess: (response) => {
      // Invalidate and refetch tenants queries
      queryClient.invalidateQueries({ queryKey: ['tenants'] })

      // Get the newly created tenant
      const newTenant = response.organization

      // Auto-switch to the newly created tenant
      if (newTenant?.id) {
        console.log('Auto-switching to newly created tenant:', newTenant.name)
        // We'll handle the auto-switch in handleCreateTenant
      }

      toast.success('Tổ chức đã được tạo thành công')
    },
    onError: (error: any) => {
      const message =
        error?.response?.data?.error || 'Không thể tạo tổ chức'
      toast.error(message)
    },
  })

  // Update tenant mutation - note: this would need the tenant ID in the URL
  const updateTenantMutation = useApiMutation<
    { organization: Tenant },
    UpdateTenantRequest & { id: string }
  >('/api/tenants', 'PUT', {
    onSuccess: () => {
      // Invalidate and refetch tenants queries
      queryClient.invalidateQueries({ queryKey: ['tenants'] })
      toast.success('Organization updated successfully')
    },
    onError: (error: any) => {
      const message =
        error?.response?.data?.error || 'Failed to update organization'
      toast.error(message)
    },
  })

  // Delete tenant mutation
  const deleteTenantMutation = useApiMutation<void, { id: string }>(
    '/api/tenants',
    'DELETE',
    {
      onSuccess: () => {
        // Invalidate and refetch tenants queries
        queryClient.invalidateQueries({ queryKey: ['tenants'] })
        toast.success('Organization deleted successfully')
      },
      onError: (error: any) => {
        const message =
          error?.response?.data?.error || 'Failed to delete organization'
        toast.error(message)
      },
    },
  )

  // Action handlers
  const handleCreateTenant = useCallback(
    async (data: CreateTenantRequest) => {
      try {
        const response = await createTenantMutation.mutateAsync(data)

        // Auto-switch to the newly created tenant
        const newTenant = response.organization
        if (newTenant?.id) {
          console.log('Auto-switching to newly created tenant:', newTenant.name)

          // Return the new tenant ID so the caller can handle the switch
          return {
            success: true,
            tenant: newTenant,
            shouldAutoSwitch: true
          }
        }

        return { success: true, tenant: newTenant, shouldAutoSwitch: false }
      } catch (error) {
        console.error('Failed to create tenant:', error)
        return { success: false, error }
      }
    },
    [createTenantMutation],
  )

  const handleUpdateTenant = useCallback(
    (id: string, data: UpdateTenantRequest) => {
      updateTenantMutation.mutate({ ...data, id })
    },
    [updateTenantMutation],
  )

  const handleDeleteTenant = useCallback(
    (id: string) => {
      deleteTenantMutation.mutate({ id })
    },
    [deleteTenantMutation],
  )

  // Refresh tenants data
  const refreshTenants = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['tenants'] })
  }, [queryClient])

  // Transform tenants data for dropdown options
  const tenantOptions = useMemo(() => {
    const organizations = tenantsQuery.data?.organizations || []

    return organizations.map((tenant) => ({
      value: tenant.id,
      label: tenant.name,
      disabled: false,
    }))
  }, [tenantsQuery.data?.organizations])

  // Get tenant by ID
  const getTenantById = useCallback(
    (id: string): Tenant | undefined => {
      const organizations = tenantsQuery.data?.organizations || []
      return organizations.find((tenant) => tenant.id === id)
    },
    [tenantsQuery.data?.organizations],
  )

  // Get tenant by name
  const getTenantByName = useCallback(
    (name: string): Tenant | undefined => {
      const organizations = tenantsQuery.data?.organizations || []
      return organizations.find((tenant) => tenant.name === name)
    },
    [tenantsQuery.data?.organizations],
  )

  return {
    // Query data
    tenants: tenantsQuery.data?.organizations || [],
    tenantsQuery,
    tenantOptions,

    // Mutations
    createTenantMutation,
    updateTenantMutation,
    deleteTenantMutation,

    // Action handlers
    handleCreateTenant,
    handleUpdateTenant,
    handleDeleteTenant,
    refreshTenants,

    // Utility functions
    getTenantById,
    getTenantByName,

    // Loading states
    isLoading: tenantsQuery.isLoading,
    isCreating: createTenantMutation.isPending,
    isUpdating: updateTenantMutation.isPending,
    isDeleting: deleteTenantMutation.isPending,

    // Error states
    error: tenantsQuery.error,
    createError: createTenantMutation.error,
    updateError: updateTenantMutation.error,
    deleteError: deleteTenantMutation.error,
  }
}

/**
 * Hook for tenant selection state management
 * Now integrated with useSelectedTenant for persistent storage
 */
export const useTenantSelection = (defaultTenantId?: string) => {
  const { tenants, tenantOptions, getTenantById, handleCreateTenant: createTenant } = useTenantActions()
  const { withLoading } = useGlobalLoading()
  const { refreshAccessToken } = useIdentityActions()

  // Use the persistent tenant selection hook
  const {
    selectedTenant: persistedTenant,
    setTenant,
    isLoading,
  } = useSelectedTenant()



  // Auto-select first tenant if no tenant is selected and tenants are available
  useEffect(() => {
    if (!isLoading && !persistedTenant && tenants.length > 0) {
      const firstTenant = tenants[0]
      if (firstTenant) {
        console.log('Auto-selecting first tenant:', firstTenant.name)
        setTenant({
          _id: firstTenant._id,
          id: firstTenant.id,
          name: firstTenant.name,
          address: firstTenant.address,
          created_by: firstTenant.created_by,
          createdAt: firstTenant.createdAt || firstTenant.created_at,
          updatedAt: firstTenant.updatedAt || firstTenant.updated_at,
        })
      }
    }
  }, [isLoading, persistedTenant, tenants, setTenant])

  // Use persisted tenant or fallback to default
  const selectedTenantId = persistedTenant?.id || defaultTenantId

  const handleTenantChange = useCallback(
    async (tenantId: string) => {
      console.log('Tenant selected:', tenantId)

      // Find the full tenant object
      const tenant = getTenantById(tenantId)
      if (!tenant) {
        toast.error('Không tìm thấy tổ chức')
        return
      }

      try {
        // Call refresh-token API with tenant switch
        await withLoading(
          async () => {
            const result = await refreshAccessToken(tenantId)

            if (result?.tenant_switched) {
              // Update persistent storage with full tenant object after successful API call
              setTenant({
                _id: tenant._id,
                id: tenant.id,
                name: tenant.name,
                address: tenant.address,
                created_by: tenant.created_by,
                createdAt: tenant.createdAt || tenant.created_at,
                updatedAt: tenant.updatedAt || tenant.updated_at,
              })

              toast.success('Đã chuyển đổi tổ chức thành công!')
            } else {
              throw new Error('Tenant switch failed')
            }
          },
          {
            message: `Đang chuyển đổi sang ${tenant.name}...`,
            priority: 5, // High priority for tenant switching
          },
        )
      } catch (error) {
        console.error('Tenant switch failed:', error)
        const message = error instanceof Error ? error.message : 'Không thể chuyển đổi tổ chức'
        toast.error(message)
      }
    },
    [getTenantById, setTenant, withLoading, refreshAccessToken],
  )

  // Handle create tenant with auto-switch
  const handleCreateTenantWithAutoSwitch = useCallback(
    async (data: CreateTenantRequest) => {
      try {
        await withLoading(
          async () => {
            const result = await createTenant(data)

            if (result.success && result.shouldAutoSwitch && result.tenant) {
              console.log('Auto-switching to newly created tenant:', result.tenant.name)

              // Call refresh token to switch to the new tenant
              const refreshResult = await refreshAccessToken(result.tenant.id)

              if (refreshResult?.tenant_switched) {
                // Update persistent storage with the new tenant
                setTenant({
                  _id: result.tenant._id,
                  id: result.tenant.id,
                  name: result.tenant.name,
                  address: result.tenant.address,
                  created_by: result.tenant.created_by,
                  createdAt: result.tenant.createdAt || result.tenant.created_at,
                  updatedAt: result.tenant.updatedAt || result.tenant.updated_at,
                })

                toast.success(`Đã tạo và chuyển đổi sang tổ chức "${result.tenant.name}" thành công!`)
              } else {
                toast.success('Tổ chức đã được tạo thành công')
              }
            } else if (result.success) {
              toast.success('Tổ chức đã được tạo thành công')
            } else {
              throw new Error('Failed to create tenant')
            }
          },
          {
            message: 'Đang tạo tổ chức mới...',
            priority: 5, // High priority for tenant creation
          },
        )
      } catch (error) {
        console.error('Failed to create tenant with auto-switch:', error)
        const message = error instanceof Error ? error.message : 'Không thể tạo tổ chức'
        toast.error(message)
      }
    },
    [createTenant, refreshAccessToken, setTenant, withLoading],
  )

  // Convert tenants to the format expected by TenantSelector (full objects)
  const tenantsForSelector = useMemo(() => {
    return tenants.map((tenant) => ({
      _id: tenant._id,
      id: tenant.id,
      name: tenant.name,
      address: tenant.address,
      created_by: tenant.created_by,
      createdAt: tenant.createdAt || tenant.created_at,
      updatedAt: tenant.updatedAt || tenant.updated_at,
    }))
  }, [tenants])

  return {
    tenants,
    tenantOptions, // Keep for backward compatibility
    tenantsForSelector, // Full tenant objects for TenantSelector
    selectedTenant: persistedTenant, // Return the persisted tenant object
    selectedTenantId,
    handleTenantChange,
    handleCreateTenantWithAutoSwitch, // New function for creating tenant with auto-switch

    // Loading states - we don't have direct access to refreshAccessToken loading state
    // but the withLoading wrapper will handle the loading UI
    isSwitchingTenant: false, // This will be handled by withLoading
    switchError: null, // Errors are handled by toast in handleTenantChange
  }
}
