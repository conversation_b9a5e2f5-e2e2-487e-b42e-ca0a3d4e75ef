import { useLoadingControl } from '@/contexts/loading-context'
import { useCallback } from 'react'

/**
 * Hook để quản lý global loading với các tính năng nâng cao
 *
 * Features:
 * - Debounced loading (chỉ hiện sau 200ms)
 * - Minimum display time (hiện ít nhất 500ms)
 * - Priority-based loading messages
 * - Smooth transitions
 * - Multiple loading tracking
 */
export function useGlobalLoading() {
  const {
    showLoading,
    hideLoading,
    showCriticalLoading,
    clearAllLoading,
    isLoading,
    isVisible,
    loadingCount
  } = useLoadingControl()

  // Wrap any async function with loading
  const withLoading = useCallback(
    async <T,>(
      asyncFn: () => Promise<T>,
      options?: {
        message?: string
        priority?: number
        id?: string
      }
    ): Promise<T> => {
      const { message = 'Đang xử lý...', priority = 0, id } = options || {}
      const loadingId = showLoading(message, priority)

      try {
        const result = await asyncFn()
        return result
      } finally {
        hideLoading(loadingId)
      }
    },
    [showLoading, hideLoading]
  )

  // Simulate API call with loading
  const simulateApiCall = useCallback(async (
    duration = 2000,
    message = '<PERSON><PERSON> tải dữ liệu...',
    priority = 0
  ) => {
    const loadingId = showLoading(message, priority)

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, duration))

      // Simulate random success/failure
      if (Math.random() > 0.8) {
        throw new Error('Simulated API error')
      }

      return { success: true, data: 'API call completed successfully' }
    } finally {
      hideLoading(loadingId)
    }
  }, [showLoading, hideLoading])

  // Simulate multiple concurrent API calls
  const simulateMultipleApiCalls = useCallback(async () => {
    const calls = [
      simulateApiCall(1500, 'Đang tải danh sách người dùng...', 1),
      simulateApiCall(2000, 'Đang tải cài đặt hệ thống...', 2),
      simulateApiCall(1000, 'Đang đồng bộ dữ liệu...', 3),
    ]

    try {
      const results = await Promise.allSettled(calls)
      return results
    } catch (error) {
      console.error('Multiple API calls failed:', error)
      throw error
    }
  }, [simulateApiCall])

  // Show critical loading (high priority)
  const showCriticalMessage = useCallback((message = 'Đang xử lý quan trọng...') => {
    return showCriticalLoading(message)
  }, [showCriticalLoading])

  return {
    // Basic loading controls
    showLoading,
    hideLoading,
    showCriticalMessage,
    clearAllLoading,

    // Loading state
    isLoading,
    isVisible,
    loadingCount,

    // Enhanced functions
    withLoading,
    simulateApiCall,
    simulateMultipleApiCalls,
  }
}

// Hook for React Query mutations
export function useLoadingMutation() {
  const { showLoading, hideLoading } = useGlobalLoading()

  const wrapMutation = useCallback(
    <T, V>(
      mutationFn: (variables: V) => Promise<T>,
      options?: {
        loadingMessage?: string
        priority?: number
        successMessage?: string
        errorMessage?: string
      }
    ) => {
      return async (variables: V): Promise<T> => {
        const { loadingMessage = 'Đang xử lý...', priority = 0 } = options || {}
        const loadingId = showLoading(loadingMessage, priority)

        try {
          const result = await mutationFn(variables)

          if (options?.successMessage) {
            // Show success message briefly
            const successId = showLoading(options.successMessage, priority + 1)
            setTimeout(() => {
              hideLoading(successId)
              hideLoading(loadingId)
            }, 500)
          } else {
            hideLoading(loadingId)
          }

          return result
        } catch (error) {
          if (options?.errorMessage) {
            // Show error message briefly
            const errorId = showLoading(options.errorMessage, priority + 1)
            setTimeout(() => {
              hideLoading(errorId)
              hideLoading(loadingId)
            }, 1000)
          } else {
            hideLoading(loadingId)
          }
          throw error
        }
      }
    },
    [showLoading, hideLoading]
  )

  return { wrapMutation }
}

// Hook for form submissions
export function useLoadingForm() {
  const { withLoading } = useGlobalLoading()

  const submitWithLoading = useCallback(
    async <T,>(
      submitFn: () => Promise<T>,
      options?: {
        loadingMessage?: string
        priority?: number
        isCreate?: boolean
        isUpdate?: boolean
        isDelete?: boolean
      }
    ): Promise<T> => {
      let message = options?.loadingMessage
      const priority = options?.priority || 2 // Form submissions have higher priority

      if (!message) {
        if (options?.isCreate) {
          message = 'Đang tạo mới...'
        } else if (options?.isUpdate) {
          message = 'Đang cập nhật...'
        } else if (options?.isDelete) {
          message = 'Đang xóa...'
        } else {
          message = 'Đang xử lý...'
        }
      }

      return withLoading(submitFn, { message, priority })
    },
    [withLoading]
  )

  return { submitWithLoading }
}

/**
 * Hook để tạo loading wrapper cho các function
 */
export function useLoadingWrapper() {
  const { showLoading, hideLoading } = useGlobalLoading()

  const withLoading = useCallback(
    <T extends (...args: any[]) => Promise<any>>(
      fn: T,
      options?: {
        message?: string
        priority?: number
        loadingId?: string
      }
    ): T => {
      return (async (...args: Parameters<T>) => {
        const { message = 'Đang xử lý...', priority = 0, loadingId } = options || {}
        const id = loadingId || showLoading(message, priority)

        try {
          const result = await fn(...args)
          return result
        } finally {
          hideLoading(id)
        }
      }) as T
    },
    [showLoading, hideLoading]
  )

  return { withLoading }
}
