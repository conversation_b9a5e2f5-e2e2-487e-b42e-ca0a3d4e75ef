import { useCallback, useEffect, useRef, useState } from 'react'
import { tracingConfig } from '../../configs/tracing.config'

/**
 * Hook to use tracing in React components
 */
export function useTracing() {
  const sessionIdRef = useRef<string | undefined>(undefined)
  const [tracer, setTracer] = useState<any>(null)
  const [isTracingEnabled, setIsTracingEnabled] = useState(false)

  // Initialize tracing and session ID
  useEffect(() => {
    const initializeTracing = async () => {
      try {
        // Check if tracing is enabled in config first
        if (!tracingConfig.enabled) {
          setIsTracingEnabled(false)
          return
        }

        // Generate session ID once
        if (!sessionIdRef.current) {
          const { generateSessionId } = await import('@c-cam/tracing-browser')
          sessionIdRef.current = generateSessionId()
        }

        // Get tracer
        const { getWebTracer } = await import('@c-cam/tracing-browser')
        const tracerInstance = getWebTracer()
        setTracer(tracerInstance)
        setIsTracingEnabled(!!tracerInstance)
      } catch (error) {
        console.warn('Failed to initialize tracing:', error)
        setIsTracingEnabled(false)
      }
    }

    initializeTracing()
  }, [])

  const trackUserEvent = useCallback(
    async (name: string, attributes: Record<string, any> = {}) => {
      try {
        // Early return if tracing is disabled
        if (!tracingConfig.enabled) {
          return
        }

        const { trackEvent } = await import('@c-cam/tracing-browser')
        trackEvent(name, {
          'event.category': 'user_interaction',
          'session.id': sessionIdRef.current,
          ...attributes,
        })
      } catch (error) {
        console.warn('Failed to track user event:', error)
      }
    },
    [],
  )

  const trackBusinessEvent = useCallback(
    async (name: string, attributes: Record<string, any> = {}) => {
      try {
        // Early return if tracing is disabled
        if (!tracingConfig.enabled) {
          return
        }

        const { trackEvent } = await import('@c-cam/tracing-browser')
        trackEvent(name, {
          'event.category': 'business_logic',
          'session.id': sessionIdRef.current,
          ...attributes,
        })
      } catch (error) {
        console.warn('Failed to track business event:', error)
      }
    },
    [],
  )

  const trackUserError = useCallback(
    async (error: Error, context: Record<string, any> = {}) => {
      try {
        // Early return if tracing is disabled
        if (!tracingConfig.enabled) {
          return
        }

        const { trackError } = await import('@c-cam/tracing-browser')
        trackError(error, {
          'error.category': 'user_error',
          'session.id': sessionIdRef.current,
          ...context,
        })
      } catch (tracingError) {
        console.warn('Failed to track user error:', tracingError)
      }
    },
    [],
  )

  const trackPageNavigation = useCallback(
    async (pageName?: string, attributes: Record<string, any> = {}) => {
      try {
        // Early return if tracing is disabled
        if (!tracingConfig.enabled) {
          return
        }

        const { trackPageView } = await import('@c-cam/tracing-browser')
        trackPageView(pageName || window.location.href, document.title, document.referrer)
      } catch (error) {
        console.warn('Failed to track page navigation:', error)
      }
    },
    [],
  )

  const setUserContext = useCallback(
    async (userContext: { userId?: string; userEmail?: string }) => {
      try {
        // Early return if tracing is disabled
        if (!tracingConfig.enabled) {
          return
        }

        if (userContext.userId) {
          const { setUserContextForTracing } = await import('@c-cam/tracing-browser')
          setUserContextForTracing({
            userId: userContext.userId,
            email: userContext.userEmail,
            attributes: {
              sessionId: sessionIdRef.current,
            },
          })
        }
      } catch (error) {
        console.warn('Failed to set user context:', error)
      }
    },
    [],
  )

  const addAttributes = useCallback(async (attributes: Record<string, any>) => {
    try {
      // Early return if tracing is disabled
      if (!tracingConfig.enabled) {
        return
      }

      const { addTraceAttributes } = await import('@c-cam/tracing-browser')
      addTraceAttributes(attributes)
    } catch (error) {
      console.warn('Failed to add attributes:', error)
    }
  }, [])

  const withSpan = useCallback(
    async <T>(
      name: string,
      operation: () => Promise<T> | T,
      attributes?: Record<string, any>,
    ): Promise<T> => {
      try {
        // If tracing is disabled, just execute the operation
        if (!tracingConfig.enabled) {
          return await operation()
        }

        const { withWebSpan } = await import('@c-cam/tracing-browser')
        return withWebSpan(name, operation, {
          'session.id': sessionIdRef.current,
          ...attributes,
        })
      } catch (error) {
        console.warn('Failed to execute with span:', error)
        // Fallback to executing without tracing
        return await operation()
      }
    },
    [],
  )

  const getTraceId = useCallback(async () => {
    try {
      // Early return if tracing is disabled
      if (!tracingConfig.enabled) {
        return undefined
      }

      const { getCurrentTraceId } = await import('@c-cam/tracing-browser')
      return getCurrentTraceId()
    } catch (error) {
      console.warn('Failed to get trace ID:', error)
      return undefined
    }
  }, [])

  const getSpanId = useCallback(async () => {
    try {
      // Early return if tracing is disabled
      if (!tracingConfig.enabled) {
        return undefined
      }

      const { getCurrentSpanId } = await import('@c-cam/tracing-browser')
      return getCurrentSpanId()
    } catch (error) {
      console.warn('Failed to get span ID:', error)
      return undefined
    }
  }, [])

  return {
    tracer,
    trackUserEvent,
    trackBusinessEvent,
    trackUserError,
    trackPageNavigation,
    setUserContext,
    addAttributes,
    withSpan,
    getTraceId,
    getSpanId,
    sessionId: sessionIdRef.current,
    isTracingEnabled,
  }
}
