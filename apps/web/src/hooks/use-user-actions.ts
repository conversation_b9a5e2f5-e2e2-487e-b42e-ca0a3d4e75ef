import { api } from '@/configs/axios'
import { useApiMutation } from '@/shared/hooks/use-api-query'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'
import { toast } from 'sonner'
import type { Member } from './use-user-query'

// Types for member operations
export interface CreateUserRequest {
  unit_id: string
  code?: string
  name: string
  email?: string
  phone?: string
  dob?: string
  gender?: string
  username: string
  password: string
  member_role_id?: string
}

export interface UpdateUserRequest {
  unit_id?: string
  code?: string
  name?: string
  email?: string
  phone?: string
  dob?: string
  gender?: string
  username?: string
  password?: string
  member_role_id?: string
}

export interface UploadAvatarRequest {
  fileName: string
  fileData: string // base64 encoded
}

export interface CreateUserResponse {
  user: Member
}

export interface UploadAvatarResponse {
  avatar_url: string
}

/**
 * Hook for member/user actions (create, update, delete, upload avatar)
 */
export function useUserActions() {
  const queryClient = useQueryClient()

  // Create member mutation
  const createUserMutation = useApiMutation<
    CreateUserResponse,
    CreateUserRequest
  >('/api/users', 'POST', {
    onSuccess: () => {
      // Invalidate and refetch members queries
      queryClient.invalidateQueries({ queryKey: ['members'] })
      toast.success('Thành viên đã được tạo thành công')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error || 'Không thể tạo thành viên'
      toast.error(message)
    },
  })

  // Update member mutation
  const updateUserMutation = useApiMutation<
    { success: boolean },
    UpdateUserRequest & { id: string }
  >('/api/users', 'PUT', {
    onSuccess: () => {
      // Invalidate and refetch members queries
      queryClient.invalidateQueries({ queryKey: ['members'] })
      toast.success('Thành viên đã được cập nhật thành công')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error || 'Không thể cập nhật thành viên'
      toast.error(message)
    },
  })

  // Delete member mutation
  const deleteUserMutation = useApiMutation<void, { id: string }>(
    '/api/users',
    'DELETE',
    {
      onSuccess: () => {
        // Invalidate and refetch members queries
        queryClient.invalidateQueries({ queryKey: ['members'] })
        toast.success('Thành viên đã được xóa thành công')
      },
      onError: (error: any) => {
        const message = error?.response?.data?.error || 'Không thể xóa thành viên'
        toast.error(message)
      },
    }
  )

  // Upload avatar mutation - using custom mutation for dynamic URL
  const uploadAvatarMutation = useMutation({
    mutationFn: async ({ id, ...data }: UploadAvatarRequest & { id: string }) => {
      const response = await api.post(`/api/users/${id}/avatar`, data)
      return response.data
    },
    onSuccess: () => {
      // Invalidate and refetch members queries
      queryClient.invalidateQueries({ queryKey: ['members'] })
      toast.success('Ảnh đại diện đã được tải lên thành công')
    },
    onError: (error: any) => {
      const message = error?.response?.data?.error || 'Không thể tải lên ảnh đại diện'
      toast.error(message)
    },
  })

  // Action handlers
  const handleCreateUser = useCallback(
    (data: CreateUserRequest) => {
      createUserMutation.mutate(data)
    },
    [createUserMutation]
  )

  const handleUpdateUser = useCallback(
    (id: string, data: UpdateUserRequest) => {
      updateUserMutation.mutate({ ...data, id })
    },
    [updateUserMutation]
  )

  const handleDeleteUser = useCallback(
    (id: string) => {
      deleteUserMutation.mutate({ id })
    },
    [deleteUserMutation]
  )

  const handleUploadAvatar = useCallback(
    (id: string, data: UploadAvatarRequest) => {
      uploadAvatarMutation.mutate({ ...data, id })
    },
    [uploadAvatarMutation]
  )

  // Create member with avatar upload
  const handleCreateUserWithAvatar = useCallback(
    async (memberData: CreateUserRequest, avatarFile?: Blob) => {
      try {
        // First create the member
        const response = await createUserMutation.mutateAsync(memberData)

        // If avatar file exists, upload it
        if (avatarFile && response?.user?.id) {
          const reader = new FileReader()
          reader.onload = () => {
            const base64 = reader.result?.toString().split(',')[1]
            if (base64) {
              handleUploadAvatar(response.user.id, {
                fileName: 'avatar.jpg',
                fileData: base64
              })
            }
          }
          reader.readAsDataURL(avatarFile)
        }

        return response
      } catch (error) {
        throw error
      }
    },
    [createUserMutation, handleUploadAvatar]
  )

  // Refresh members data
  const refreshMembers = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['members'] })
  }, [queryClient])

  return {
    // Mutations
    createUserMutation,
    updateUserMutation,
    deleteUserMutation,
    uploadAvatarMutation,

    // Action handlers
    handleCreateMember: handleCreateUser,
    handleCreateMemberWithAvatar: handleCreateUserWithAvatar,
    handleUpdateMember: handleUpdateUser,
    handleDeleteMember: handleDeleteUser,
    handleUploadAvatar,
    refreshMembers,

    // Loading states
    isCreating: createUserMutation.isPending,
    isUpdating: updateUserMutation.isPending,
    isDeleting: deleteUserMutation.isPending,
    isUploadingAvatar: uploadAvatarMutation.isPending,

    // Error states
    createError: createUserMutation.error,
    updateError: updateUserMutation.error,
    deleteError: deleteUserMutation.error,
    uploadError: uploadAvatarMutation.error,
  }
}
